﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Rate Benchmarking | Contractor Rate Calculator | Calculator City</title>
    <meta name="description" content="Compare contractor rates across Australian industries. Find market rates for your skills, experience level, and location. Benchmark your contract rates against industry standards.">
    <meta name="keywords" content="contract rate benchmarking,contractor rates,freelance rates,consulting rates,market rates australia,contractor salary,hourly rates,daily rates">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .benchmark-card {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .benchmark-card.recommended {
            border-color: #16a34a;
            background: #f0fdf4;
        }

        .rate-range {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1rem 0;
        }

        .rate-bar {
            flex: 1;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            position: relative;
        }

        .rate-indicator {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #16a34a);
            border-radius: 4px;
            position: relative;
        }

        .rate-marker {
            position: absolute;
            top: -4px;
            width: 16px;
            height: 16px;
            background: #1f2937;
            border-radius: 50%;
            transform: translateX(-50%);
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .benchmark-card {
                padding: 1rem;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Calculator City Header -->
        <div class="bg-orange-100/90 border-b border-orange-200 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📊 Compare!</span>
                        <span class="text-sm sm:text-base">Check <a class="font-semibold text-orange-600 hover:underline" href="#calculator">market contract rates</a><span class="hidden sm:inline"> - Know your worth!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 hover:bg-orange-200/70" href="#calculator">Check Rates</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Contract Rate Benchmarking
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Compare your contract rates against Australian market standards. Find out what contractors in your industry and experience level are earning.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto" id="calculator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Your Details</h2>
                        
                        <form class="grid gap-4" id="benchmark-form">
                            <!-- Industry -->
                            <div class="input-group">
                                <label for="industry">Industry</label>
                                <select 
                                    id="industry"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateBenchmark()"
                                >
                                    <option value="it">Information Technology</option>
                                    <option value="finance">Finance & Banking</option>
                                    <option value="consulting">Management Consulting</option>
                                    <option value="marketing">Marketing & Digital</option>
                                    <option value="engineering">Engineering</option>
                                    <option value="design">Design & Creative</option>
                                    <option value="legal">Legal Services</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="construction">Construction</option>
                                    <option value="education">Education & Training</option>
                                </select>
                            </div>

                            <!-- Role/Skill -->
                            <div class="input-group">
                                <label for="role">Role/Specialization</label>
                                <select 
                                    id="role"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateBenchmark()"
                                >
                                    <option value="developer">Software Developer</option>
                                    <option value="analyst">Business Analyst</option>
                                    <option value="manager">Project Manager</option>
                                    <option value="consultant">Senior Consultant</option>
                                    <option value="architect">Solution Architect</option>
                                    <option value="designer">UX/UI Designer</option>
                                    <option value="specialist">Technical Specialist</option>
                                </select>
                            </div>

                            <!-- Experience Level -->
                            <div class="input-group">
                                <label for="experience">Experience Level</label>
                                <select 
                                    id="experience"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateBenchmark()"
                                >
                                    <option value="junior">Junior (0-2 years)</option>
                                    <option value="mid" selected>Mid-level (3-5 years)</option>
                                    <option value="senior">Senior (6-10 years)</option>
                                    <option value="lead">Lead/Principal (10+ years)</option>
                                </select>
                            </div>

                            <!-- Location -->
                            <div class="input-group">
                                <label for="location">Location</label>
                                <select 
                                    id="location"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateBenchmark()"
                                >
                                    <option value="sydney">Sydney</option>
                                    <option value="melbourne">Melbourne</option>
                                    <option value="brisbane">Brisbane</option>
                                    <option value="perth">Perth</option>
                                    <option value="adelaide">Adelaide</option>
                                    <option value="canberra">Canberra</option>
                                    <option value="remote">Remote</option>
                                    <option value="regional">Regional</option>
                                </select>
                            </div>

                            <!-- Contract Type -->
                            <div class="input-group">
                                <label for="contractType">Contract Type</label>
                                <select 
                                    id="contractType"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateBenchmark()"
                                >
                                    <option value="short">Short-term (< 3 months)</option>
                                    <option value="medium" selected>Medium-term (3-12 months)</option>
                                    <option value="long">Long-term (12+ months)</option>
                                </select>
                            </div>

                            <!-- Current Rate (Optional) -->
                            <div class="input-group">
                                <label for="currentRate">Your Current Rate (Optional)</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input 
                                        type="number" 
                                        id="currentRate"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                        min="0"
                                        placeholder="Enter your current hourly rate"
                                        oninput="calculateBenchmark()"
                                    />
                                    <span class="flex items-center px-3 h-10">/hour</span>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Results Section -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Market Benchmark</h2>
                        
                        <!-- Rate Range Visualization -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-3">Market Rate Range</h3>
                            <div class="rate-range">
                                <span class="text-sm font-medium">$50</span>
                                <div class="rate-bar">
                                    <div class="rate-indicator" style="width: 100%;">
                                        <div class="rate-marker" id="rate-marker" style="left: 60%;"></div>
                                    </div>
                                </div>
                                <span class="text-sm font-medium">$150</span>
                            </div>
                            <div class="flex justify-between text-xs text-muted-foreground mt-1">
                                <span>Low</span>
                                <span>Market Average</span>
                                <span>High</span>
                            </div>
                        </div>

                        <div class="results-container" id="benchmark-results">
                            <div class="result-item">
                                <span class="result-label">Market Low</span>
                                <span class="result-value" id="market-low">$65/hour</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Market Average</span>
                                <span class="result-value" id="market-average">$85/hour</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Market High</span>
                                <span class="result-value" id="market-high">$120/hour</span>
                            </div>
                            <div class="result-item border-t-2 border-primary pt-4">
                                <span class="result-label font-semibold">Recommended Rate</span>
                                <span class="result-value text-xl font-bold" id="recommended-rate">$85/hour</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Daily Rate (8 hours)</span>
                                <span class="result-value" id="daily-rate">$680</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Annual Potential (1800 hours)</span>
                                <span class="result-value" id="annual-potential">$153,000</span>
                            </div>
                        </div>

                        <!-- Your Rate Comparison -->
                        <div class="mt-6" id="rate-comparison" style="display: none;">
                            <h4 class="font-semibold mb-2">Your Rate Analysis</h4>
                            <div class="p-4 rounded-lg" id="comparison-result">
                                <p class="text-sm" id="comparison-text"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 Calculator City.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Market rate data by industry, role, experience, and location
        const MARKET_RATES = {
            it: {
                developer: { junior: [45, 65, 85], mid: [65, 85, 110], senior: [85, 110, 140], lead: [110, 140, 180] },
                analyst: { junior: [40, 55, 75], mid: [55, 75, 95], senior: [75, 95, 125], lead: [95, 125, 160] },
                manager: { junior: [50, 70, 90], mid: [70, 90, 120], senior: [90, 120, 150], lead: [120, 150, 200] },
                architect: { junior: [60, 80, 100], mid: [80, 100, 130], senior: [100, 130, 170], lead: [130, 170, 220] }
            },
            finance: {
                analyst: { junior: [45, 60, 80], mid: [60, 80, 105], senior: [80, 105, 135], lead: [105, 135, 175] },
                consultant: { junior: [50, 70, 90], mid: [70, 90, 120], senior: [90, 120, 155], lead: [120, 155, 200] },
                manager: { junior: [55, 75, 95], mid: [75, 95, 125], senior: [95, 125, 160], lead: [125, 160, 210] }
            },
            consulting: {
                consultant: { junior: [50, 70, 95], mid: [70, 95, 125], senior: [95, 125, 165], lead: [125, 165, 220] },
                manager: { junior: [60, 80, 105], mid: [80, 105, 140], senior: [105, 140, 180], lead: [140, 180, 240] },
                specialist: { junior: [55, 75, 100], mid: [75, 100, 130], senior: [100, 130, 170], lead: [130, 170, 225] }
            }
        };

        // Location and contract multipliers
        const LOCATION_MULTIPLIERS = {
            sydney: 1.15, melbourne: 1.10, brisbane: 0.95, perth: 0.90, adelaide: 0.85,
            canberra: 1.05, remote: 0.90, regional: 0.75
        };

        const CONTRACT_MULTIPLIERS = {
            short: 1.20, medium: 1.00, long: 0.95
        };

        // Initialize calculator
        document.addEventListener('DOMContentLoaded', function() {
            calculateBenchmark();
        });

        // Calculate benchmark rates
        function calculateBenchmark() {
            const industry = document.getElementById('industry').value;
            const role = document.getElementById('role').value;
            const experience = document.getElementById('experience').value;
            const location = document.getElementById('location').value;
            const contractType = document.getElementById('contractType').value;
            const currentRate = parseFloat(document.getElementById('currentRate').value) || 0;

            // Get base rates
            let baseRates = [65, 85, 120]; // Default rates

            if (MARKET_RATES[industry] && MARKET_RATES[industry][role] && MARKET_RATES[industry][role][experience]) {
                baseRates = MARKET_RATES[industry][role][experience];
            } else if (MARKET_RATES[industry] && MARKET_RATES[industry]['consultant'] && MARKET_RATES[industry]['consultant'][experience]) {
                baseRates = MARKET_RATES[industry]['consultant'][experience];
            }

            // Apply multipliers
            const locationMultiplier = LOCATION_MULTIPLIERS[location] || 1.0;
            const contractMultiplier = CONTRACT_MULTIPLIERS[contractType] || 1.0;

            // Calculate adjusted rates
            const marketLow = Math.round(baseRates[0] * locationMultiplier * contractMultiplier);
            const marketAverage = Math.round(baseRates[1] * locationMultiplier * contractMultiplier);
            const marketHigh = Math.round(baseRates[2] * locationMultiplier * contractMultiplier);

            // Calculate derived values
            const recommendedRate = marketAverage;
            const dailyRate = recommendedRate * 8;
            const annualPotential = recommendedRate * 1800;

            // Update display
            document.getElementById('market-low').textContent = `$${marketLow}/hour`;
            document.getElementById('market-average').textContent = `$${marketAverage}/hour`;
            document.getElementById('market-high').textContent = `$${marketHigh}/hour`;
            document.getElementById('recommended-rate').textContent = `$${recommendedRate}/hour`;
            document.getElementById('daily-rate').textContent = `$${dailyRate.toLocaleString()}`;
            document.getElementById('annual-potential').textContent = `$${annualPotential.toLocaleString()}`;

            // Update rate visualization
            updateRateVisualization(marketLow, marketAverage, marketHigh, currentRate);

            // Show rate comparison if current rate is provided
            if (currentRate > 0) {
                showRateComparison(currentRate, marketLow, marketAverage, marketHigh);
            } else {
                document.getElementById('rate-comparison').style.display = 'none';
            }
        }

        // Update rate visualization
        function updateRateVisualization(low, average, high, currentRate) {
            const marker = document.getElementById('rate-marker');
            const range = high - low;
            const averagePosition = ((average - low) / range) * 100;

            marker.style.left = `${averagePosition}%`;

            // Update range labels
            const rangeContainer = document.querySelector('.rate-range');
            const labels = rangeContainer.querySelectorAll('span');
            labels[0].textContent = `$${low}`;
            labels[1].textContent = `$${high}`;
        }

        // Show rate comparison
        function showRateComparison(currentRate, low, average, high) {
            const comparisonDiv = document.getElementById('rate-comparison');
            const resultDiv = document.getElementById('comparison-result');
            const textElement = document.getElementById('comparison-text');

            let message = '';
            let className = '';

            if (currentRate < low) {
                message = `Your rate of $${currentRate}/hour is below market range. Consider increasing to at least $${low}/hour.`;
                className = 'bg-red-50 border border-red-200 text-red-800';
            } else if (currentRate >= low && currentRate < average) {
                message = `Your rate of $${currentRate}/hour is in the lower market range. You could potentially increase to $${average}/hour.`;
                className = 'bg-yellow-50 border border-yellow-200 text-yellow-800';
            } else if (currentRate >= average && currentRate <= high) {
                message = `Your rate of $${currentRate}/hour is competitive and within the market range.`;
                className = 'bg-green-50 border border-green-200 text-green-800';
            } else {
                message = `Your rate of $${currentRate}/hour is above market range. This may be justified by specialized skills or experience.`;
                className = 'bg-blue-50 border border-blue-200 text-blue-800';
            }

            textElement.textContent = message;
            resultDiv.className = `p-4 rounded-lg ${className}`;
            comparisonDiv.style.display = 'block';
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>
</body>
</html>
