# PowerShell script to fix email addresses that were incorrectly replaced

$htmlFiles = Get-ChildItem -Path "." -Filter "*.html"

Write-Host "Fixing email addresses in $($htmlFiles.Count) HTML files..."

foreach ($file in $htmlFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # Fix incorrectly replaced email addresses
    $content = $content -replace "Calculator Cityculatorhq@gmail\.com", "<EMAIL>"
    $content = $content -replace "Calculator Cityculatorhq", "calculatorcityhq"
    
    # Fix Ko-fi links
    $content = $content -replace "ko-fi\.com/Calculator City", "ko-fi.com/calculatorcity"
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        Write-Host "Fixed $($file.Name)"
    } else {
        Write-Host "No fixes needed for $($file.Name)"
    }
}

Write-Host "Email fix complete!"
