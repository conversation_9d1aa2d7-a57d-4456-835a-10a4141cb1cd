// Script to fix all paycal.com.au links in HTML files
const fs = require('fs');
const path = require('path');

// Mapping of paycal.com.au URLs to local files
const linkMappings = {
    'https://paycal.com.au/': 'index.html',
    'https://paycal.com.au/contractor-pay-calculator/': 'contractor-pay-calculator.html',
    'https://paycal.com.au/mortgage-calculator/': 'mortgage-calculator.html',
    'https://paycal.com.au/mortgage-payoff-calculator/': 'mortgage-payoff-calculator.html',
    'https://paycal.com.au/ev-ice-novated-lease-calculator/': 'novated-lease-calculator.html',
    'https://paycal.com.au/salary-rates/': 'salary-rate-benchmarking.html',
    'https://paycal.com.au/contract-rates/': 'contract-rate-benchmarking.html',
    'https://paycal.com.au/home-loan-comparison/': 'home-loan-comparison.html',
    'https://paycal.com.au/rate-cut-calculator/': 'rate-cut-calculator.html',
    'https://paycal.com.au/tax-cut-calculator/': 'tax-cut-calculator.html',
    'https://paycal.com.au/tax-calendar/': 'tax-calendar.html',
    'https://paycal.com.au/gst-calculator/': 'gst-calculator.html',
    'https://paycal.com.au/investment-calculator/': 'investment-calculator.html',
    'https://paycal.com.au/loan-calculator/': 'loan-calculator.html',
    'https://paycal.com.au/savings-calculator/': 'savings-calculator.html',
    'https://paycal.com.au/car-loan-calculator/': 'car-loan-calculator.html',
    'https://paycal.com.au/fuel-cost-calculator/': 'fuel-cost-calculator.html',
    'https://paycal.com.au/annual-leave-calculator/': 'annual-leave-calculator.html',
    'https://paycal.com.au/long-service-leave-calculator/': 'long-service-leave-calculator.html',
    'https://paycal.com.au/sick-leave-calculator/': 'sick-leave-calculator.html',
    'https://paycal.com.au/blogs/': '#blogs',
    'https://paycal.com.au/recommended/': '#recommended',
    'https://paycal.com.au/privacy-policy/': '#privacy',
    'https://paycal.com.au/tip-kofi-alt.gif': 'tip-kofi-alt.gif'
};

function fixLinksInFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        
        for (const [oldUrl, newUrl] of Object.entries(linkMappings)) {
            const regex = new RegExp(oldUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
            if (content.includes(oldUrl)) {
                content = content.replace(regex, newUrl);
                modified = true;
                console.log(`Fixed ${oldUrl} -> ${newUrl} in ${filePath}`);
            }
        }
        
        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Updated ${filePath}`);
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}

// Get all HTML files in current directory
const htmlFiles = fs.readdirSync('.').filter(file => file.endsWith('.html'));

console.log('Fixing paycal.com.au links in HTML files...');
htmlFiles.forEach(fixLinksInFile);
console.log('Done!');
