// Universal navigation fix for inner pages
function initializeInnerPageNavigation() {
    const dropdownGroups = document.querySelectorAll('.relative.group');
    let currentOpenDropdown = null;
    let hoverTimeout = null;

    dropdownGroups.forEach(group => {
        const button = group.querySelector('.submenu-trigger');
        const dropdown = group.querySelector('div[class*="group-hover:block"], [data-dropdown]');
        
        if (!button || !dropdown) return;

        // Remove group-hover:block class and add data-dropdown
        dropdown.classList.remove('group-hover:block');
        dropdown.setAttribute('data-dropdown', '');

        // Show dropdown on mouse enter
        button.addEventListener('mouseenter', function() {
            // Clear any pending hide timeout
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Close current open dropdown if different
            if (currentOpenDropdown && currentOpenDropdown !== dropdown) {
                currentOpenDropdown.classList.add('hidden');
            }

            // Show current dropdown
            dropdown.classList.remove('hidden');
            currentOpenDropdown = dropdown;
        });

        // Handle mouse leave from button
        button.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                if (currentOpenDropdown === dropdown && !dropdown.matches(':hover')) {
                    dropdown.classList.add('hidden');
                    currentOpenDropdown = null;
                }
            }, 150);
        });

        // Keep dropdown open when hovering over dropdown content
        dropdown.addEventListener('mouseenter', function() {
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
        });

        dropdown.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                if (currentOpenDropdown === dropdown) {
                    dropdown.classList.add('hidden');
                    currentOpenDropdown = null;
                }
            }, 150);
        });
    });

    // Close all dropdowns when clicking outside navigation
    document.addEventListener('click', function(e) {
        if (!e.target.closest('nav')) {
            if (currentOpenDropdown) {
                currentOpenDropdown.classList.add('hidden');
                currentOpenDropdown = null;
            }
        }
    });
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeInnerPageNavigation();
});
