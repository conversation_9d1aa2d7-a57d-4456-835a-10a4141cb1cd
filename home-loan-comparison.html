﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home Loan Comparison Calculator | Compare Mortgages | Calculator City</title>
    <meta name="description" content="Compare Australian home loans side-by-side. Calculate and compare mortgage repayments, interest rates, fees, and total costs across multiple lenders to find the best home loan deal.">
    <meta name="keywords" content="home loan comparison,mortgage comparison,compare home loans,mortgage calculator comparison,home loan rates comparison,best home loan,mortgage comparison tool,australian home loan comparison">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid hsl(var(--border));
            padding: 0.75rem;
            text-align: left;
        }

        .comparison-table th {
            background: hsl(var(--muted));
            font-weight: 600;
        }

        .comparison-table .winner {
            background: #dcfce7;
            font-weight: 600;
            color: #166534;
        }

        .loan-option {
            border: 2px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .loan-option.best {
            border-color: #16a34a;
            background: #f0fdf4;
        }

        .loan-option h3 {
            margin: 0 0 1rem 0;
            color: hsl(var(--foreground));
        }

        .loan-option.best h3 {
            color: #166534;
        }

        .savings-highlight {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: var(--radius);
            padding: 1rem;
            margin: 1rem 0;
            color: #92400e;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .comparison-table {
                font-size: 0.875rem;
            }
            .comparison-table th,
            .comparison-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Calculator City Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">🏠 New!</span>
                        <span class="text-sm sm:text-base">Compare <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="#calculator">home loans</a><span class="hidden sm:inline"> - Find the best mortgage deal!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#calculator">Compare Now</a>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#calculator">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                            <span class="sr-only">Compare Now</span>
                        </a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Home Loan Comparison Calculator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Compare Australian home loans side-by-side. Calculate and compare mortgage repayments, interest rates, fees, and total costs across multiple lenders.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="max-w-6xl mx-auto" id="calculator">
                    <!-- Loan Details Input -->
                    <div class="calculator-container p-6 mb-8">
                        <h2 class="text-2xl font-semibold mb-4">Loan Details</h2>

                        <form class="grid grid-cols-1 md:grid-cols-3 gap-4" id="loan-form">
                            <div class="input-group">
                                <label for="loanAmount">Loan Amount</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input type="number" id="loanAmount" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" min="0" placeholder="Enter loan amount" value="500000" oninput="compareLoans()">
                                </div>
                            </div>

                            <div class="input-group">
                                <label for="loanTerm">Loan Term (Years)</label>
                                <input type="number" id="loanTerm" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" placeholder="Enter loan term" value="30" oninput="compareLoans()">
                            </div>

                            <div class="input-group">
                                <label for="repaymentType">Repayment Type</label>
                                <select id="repaymentType" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" onchange="compareLoans()">
                                    <option value="principal-interest">Principal & Interest</option>
                                    <option value="interest-only">Interest Only</option>
                                </select>
                            </div>
                        </form>
                    </div>

                    <!-- Loan Comparison -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Loan Option 1 -->
                        <div class="loan-option" id="loan1">
                            <h3>Loan Option 1</h3>
                            <div class="input-group">
                                <label for="rate1">Interest Rate (%)</label>
                                <input type="number" step="0.01" id="rate1" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" value="6.5" oninput="compareLoans()">
                            </div>
                            <div class="input-group">
                                <label for="fees1">Upfront Fees</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input type="number" id="fees1" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" value="600" oninput="compareLoans()">
                                </div>
                            </div>
                            <div class="input-group">
                                <label for="monthlyFee1">Monthly Fee</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input type="number" id="monthlyFee1" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" value="10" oninput="compareLoans()">
                                </div>
                            </div>
                            <div class="results-container mt-4">
                                <div class="result-item">
                                    <span class="result-label">Monthly Payment</span>
                                    <span class="result-value" id="payment1">$3,160</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">Total Interest</span>
                                    <span class="result-value" id="interest1">$637,719</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">Total Cost</span>
                                    <span class="result-value" id="total1">$1,138,319</span>
                                </div>
                            </div>
                        </div>

                        <!-- Loan Option 2 -->
                        <div class="loan-option" id="loan2">
                            <h3>Loan Option 2</h3>
                            <div class="input-group">
                                <label for="rate2">Interest Rate (%)</label>
                                <input type="number" step="0.01" id="rate2" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" value="6.2" oninput="compareLoans()">
                            </div>
                            <div class="input-group">
                                <label for="fees2">Upfront Fees</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input type="number" id="fees2" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" value="800" oninput="compareLoans()">
                                </div>
                            </div>
                            <div class="input-group">
                                <label for="monthlyFee2">Monthly Fee</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input type="number" id="monthlyFee2" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" value="15" oninput="compareLoans()">
                                </div>
                            </div>
                            <div class="results-container mt-4">
                                <div class="result-item">
                                    <span class="result-label">Monthly Payment</span>
                                    <span class="result-value" id="payment2">$3,071</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">Total Interest</span>
                                    <span class="result-value" id="interest2">$605,560</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">Total Cost</span>
                                    <span class="result-value" id="total2">$1,106,360</span>
                                </div>
                            </div>
                        </div>

                        <!-- Loan Option 3 -->
                        <div class="loan-option" id="loan3">
                            <h3>Loan Option 3</h3>
                            <div class="input-group">
                                <label for="rate3">Interest Rate (%)</label>
                                <input type="number" step="0.01" id="rate3" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" value="5.9" oninput="compareLoans()">
                            </div>
                            <div class="input-group">
                                <label for="fees3">Upfront Fees</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input type="number" id="fees3" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" value="1200" oninput="compareLoans()">
                                </div>
                            </div>
                            <div class="input-group">
                                <label for="monthlyFee3">Monthly Fee</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input type="number" id="monthlyFee3" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" value="20" oninput="compareLoans()">
                                </div>
                            </div>
                            <div class="results-container mt-4">
                                <div class="result-item">
                                    <span class="result-label">Monthly Payment</span>
                                    <span class="result-value" id="payment3">$2,985</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">Total Interest</span>
                                    <span class="result-value" id="interest3">$574,600</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">Total Cost</span>
                                    <span class="result-value" id="total3">$1,075,800</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Savings Summary -->
                    <div class="savings-highlight" id="savings-summary">
                        <h3>💰 Best Deal: Loan Option 3</h3>
                        <p>You could save <strong>$62,519</strong> over the life of the loan compared to the most expensive option!</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 Calculator City.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Calculate monthly payment
        function calculateMonthlyPayment(principal, annualRate, years) {
            const monthlyRate = annualRate / 100 / 12;
            const numberOfPayments = years * 12;

            if (monthlyRate === 0) {
                return principal / numberOfPayments;
            }

            return principal * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) /
                   (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Compare loans
        function compareLoans() {
            const loanAmount = parseFloat(document.getElementById('loanAmount').value) || 500000;
            const loanTerm = parseFloat(document.getElementById('loanTerm').value) || 30;

            const loans = [
                {
                    rate: parseFloat(document.getElementById('rate1').value) || 6.5,
                    fees: parseFloat(document.getElementById('fees1').value) || 600,
                    monthlyFee: parseFloat(document.getElementById('monthlyFee1').value) || 10,
                    id: '1'
                },
                {
                    rate: parseFloat(document.getElementById('rate2').value) || 6.2,
                    fees: parseFloat(document.getElementById('fees2').value) || 800,
                    monthlyFee: parseFloat(document.getElementById('monthlyFee2').value) || 15,
                    id: '2'
                },
                {
                    rate: parseFloat(document.getElementById('rate3').value) || 5.9,
                    fees: parseFloat(document.getElementById('fees3').value) || 1200,
                    monthlyFee: parseFloat(document.getElementById('monthlyFee3').value) || 20,
                    id: '3'
                }
            ];

            let bestLoan = null;
            let lowestTotalCost = Infinity;

            loans.forEach(loan => {
                const monthlyPayment = calculateMonthlyPayment(loanAmount, loan.rate, loanTerm);
                const totalPayments = monthlyPayment * loanTerm * 12;
                const totalInterest = totalPayments - loanAmount;
                const totalFees = loan.fees + (loan.monthlyFee * loanTerm * 12);
                const totalCost = totalPayments + loan.fees + (loan.monthlyFee * loanTerm * 12);
                const monthlyTotal = monthlyPayment + loan.monthlyFee;

                // Update display
                document.getElementById(`payment${loan.id}`).textContent = formatCurrency(monthlyTotal);
                document.getElementById(`interest${loan.id}`).textContent = formatCurrency(totalInterest);
                document.getElementById(`total${loan.id}`).textContent = formatCurrency(totalCost);

                // Track best loan
                if (totalCost < lowestTotalCost) {
                    lowestTotalCost = totalCost;
                    bestLoan = loan.id;
                }

                // Remove previous winner classes
                document.getElementById(`loan${loan.id}`).classList.remove('best');
            });

            // Highlight best loan
            if (bestLoan) {
                document.getElementById(`loan${bestLoan}`).classList.add('best');

                // Update savings summary
                const highestCost = Math.max(...loans.map(loan => {
                    const monthlyPayment = calculateMonthlyPayment(loanAmount, loan.rate, loanTerm);
                    const totalPayments = monthlyPayment * loanTerm * 12;
                    return totalPayments + loan.fees + (loan.monthlyFee * loanTerm * 12);
                }));

                const savings = highestCost - lowestTotalCost;
                document.getElementById('savings-summary').innerHTML = `
                    <h3>💰 Best Deal: Loan Option ${bestLoan}</h3>
                    <p>You could save <strong>${formatCurrency(savings)}</strong> over the life of the loan compared to the most expensive option!</p>
                `;
            }
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            compareLoans();
        });
    </script>
</body>
</html>
