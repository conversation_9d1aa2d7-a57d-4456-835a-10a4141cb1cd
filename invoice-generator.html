﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Invoice Generator | Create Professional Invoices | Calculator City</title>
    <meta name="description" content="Free online invoice generator for Australian businesses. Create professional invoices with GST calculations, ABN details, and payment terms. Download as PDF instantly.">
    <meta name="keywords" content="invoice generator,free invoice generator,australian invoice,gst invoice,abn invoice,business invoice,professional invoice,invoice template,invoice maker,pdf invoice">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus, .input-group textarea:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .btn-secondary {
            background: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
            padding: 0.75rem 1.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-secondary:hover {
            background: hsl(var(--accent));
        }

        .invoice-preview {
            background: white;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 2rem;
            margin-top: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 2rem;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 1rem;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .invoice-table th,
        .invoice-table td {
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
            text-align: left;
        }

        .invoice-table th {
            background-color: #f9fafb;
            font-weight: 600;
        }

        .invoice-total {
            margin-top: 1rem;
            text-align: right;
        }

        .invoice-total .total-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .invoice-total .total-row.final {
            font-weight: 600;
            font-size: 1.1rem;
            border-bottom: 2px solid #374151;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .invoice-preview {
                padding: 1rem;
            }
            .invoice-header {
                flex-direction: column;
                gap: 1rem;
            }
        }

        @media print {
            .no-print {
                display: none !important;
            }
            .invoice-preview {
                box-shadow: none;
                border: none;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Navigation Bar -->
        <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <a class="mr-6 flex items-center space-x-2" href="index.html">
                        <span class="hidden font-bold sm:inline-block">Calculator City</span>
                    </a>
                    <nav class="flex items-center space-x-6 text-sm font-medium">
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Calculator City.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="salary-tax-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Tax Calculator</a>
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>

                <!-- Mobile menu button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Mobile menu -->
                <div class="fixed left-0 top-0 z-50 grid h-[calc(100vh-4rem)] w-full translate-x-[-100%] grid-flow-row auto-rows-max overflow-auto p-6 pb-32 shadow-md animate-in slide-in-from-bottom-80 md:hidden hidden" id="mobile-menu">
                    <div class="relative z-20 grid gap-6 rounded-md bg-popover p-4 text-popover-foreground shadow-md">
                        <a class="flex items-center space-x-2" href="index.html">
                            <span class="font-bold">Calculator City</span>
                        </a>
                        <nav class="grid gap-2">
                            <a href="index.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Australian Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate salary, tax and superannuation</p>
                            </a>
                            <a href="contractor-pay-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Contractor Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate contractor rates and take-home pay</p>
                            </a>
                            <a href="mortgage-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Mortgage Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate home loan repayments</p>
                            </a>
                            <a href="novated-lease-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Novated Lease Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Compare EV vs ICE vehicle costs</p>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- Calculator City Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📄 Featured!</span>
                        <span class="text-sm sm:text-base">Create professional <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="#generator">invoices instantly</a><span class="hidden sm:inline"> - Free Australian invoice generator with GST!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#generator">Try Now</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Free Invoice Generator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Create professional invoices for your Australian business. Include GST calculations, ABN details, and payment terms. Download as PDF instantly.
                    </p>
                </div>

                <!-- Invoice Generator -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto" id="generator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Invoice Details</h2>

                        <form class="grid gap-4" id="invoice-form">
                            <!-- Business Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Your Business</h3>
                                <div class="grid gap-3">
                                    <div class="input-group">
                                        <label for="businessName">Business Name</label>
                                        <input type="text" id="businessName" placeholder="Your Business Name" value="ABC Consulting Pty Ltd" oninput="updateInvoice()">
                                    </div>
                                    <div class="input-group">
                                        <label for="abn">ABN</label>
                                        <input type="text" id="abn" placeholder="**************" value="**************" oninput="updateInvoice()">
                                    </div>
                                    <div class="input-group">
                                        <label for="businessAddress">Address</label>
                                        <textarea id="businessAddress" rows="2" placeholder="Business address" oninput="updateInvoice()">123 Business St
Sydney NSW 2000</textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Client Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Bill To</h3>
                                <div class="grid gap-3">
                                    <div class="input-group">
                                        <label for="clientName">Client Name</label>
                                        <input type="text" id="clientName" placeholder="Client Name" value="XYZ Corporation" oninput="updateInvoice()">
                                    </div>
                                    <div class="input-group">
                                        <label for="clientAddress">Client Address</label>
                                        <textarea id="clientAddress" rows="2" placeholder="Client address" oninput="updateInvoice()">456 Client Ave
Melbourne VIC 3000</textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Invoice Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Invoice Info</h3>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="input-group">
                                        <label for="invoiceNumber">Invoice #</label>
                                        <input type="text" id="invoiceNumber" placeholder="INV-001" value="INV-001" oninput="updateInvoice()">
                                    </div>
                                    <div class="input-group">
                                        <label for="invoiceDate">Date</label>
                                        <input type="date" id="invoiceDate" oninput="updateInvoice()">
                                    </div>
                                    <div class="input-group">
                                        <label for="dueDate">Due Date</label>
                                        <input type="date" id="dueDate" oninput="updateInvoice()">
                                    </div>
                                    <div class="input-group">
                                        <label for="gstRegistered">GST Registered</label>
                                        <select id="gstRegistered" onchange="updateInvoice()">
                                            <option value="yes">Yes</option>
                                            <option value="no">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Line Items -->
                            <div>
                                <h3 class="text-lg font-semibold mb-3">Items</h3>
                                <div id="line-items">
                                    <div class="line-item grid grid-cols-12 gap-2 mb-2">
                                        <div class="col-span-6">
                                            <input type="text" placeholder="Description" value="Consulting Services" class="item-description" oninput="updateInvoice()">
                                        </div>
                                        <div class="col-span-2">
                                            <input type="number" placeholder="Qty" value="1" class="item-quantity" oninput="updateInvoice()">
                                        </div>
                                        <div class="col-span-2">
                                            <input type="number" placeholder="Rate" value="150" class="item-rate" oninput="updateInvoice()">
                                        </div>
                                        <div class="col-span-2">
                                            <input type="text" placeholder="Amount" class="item-amount" readonly>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn-secondary mt-2" onclick="addLineItem()">Add Item</button>
                            </div>

                            <!-- Actions -->
                            <div class="flex gap-2 pt-4">
                                <button type="button" class="btn-primary" onclick="downloadPDF()">Download PDF</button>
                                <button type="button" class="btn-secondary" onclick="printInvoice()">Print</button>
                            </div>
                        </form>
                    </div>

                    <!-- Invoice Preview -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Invoice Preview</h2>

                        <div class="invoice-preview" id="invoice-preview">
                            <div class="invoice-header">
                                <div>
                                    <h1 class="text-2xl font-bold" id="preview-business-name">ABC Consulting Pty Ltd</h1>
                                    <p class="text-sm text-gray-600">ABN: <span id="preview-abn">**************</span></p>
                                    <div class="text-sm text-gray-600 mt-2" id="preview-business-address">
                                        123 Business St<br>
                                        Sydney NSW 2000
                                    </div>
                                </div>
                                <div class="text-right">
                                    <h2 class="text-xl font-bold">INVOICE</h2>
                                    <p class="text-sm">Invoice #: <span id="preview-invoice-number">INV-001</span></p>
                                    <p class="text-sm">Date: <span id="preview-invoice-date"></span></p>
                                    <p class="text-sm">Due: <span id="preview-due-date"></span></p>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h3 class="font-semibold mb-2">Bill To:</h3>
                                <div id="preview-client-name" class="font-medium">XYZ Corporation</div>
                                <div id="preview-client-address" class="text-sm text-gray-600">
                                    456 Client Ave<br>
                                    Melbourne VIC 3000
                                </div>
                            </div>

                            <table class="invoice-table">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Qty</th>
                                        <th>Rate</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody id="preview-items">
                                    <tr>
                                        <td>Consulting Services</td>
                                        <td>1</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="invoice-total">
                                <div class="total-row">
                                    <span>Subtotal:</span>
                                    <span id="preview-subtotal">$150.00</span>
                                </div>
                                <div class="total-row" id="gst-row">
                                    <span>GST (10%):</span>
                                    <span id="preview-gst">$15.00</span>
                                </div>
                                <div class="total-row final">
                                    <span>Total:</span>
                                    <span id="preview-total">$165.00</span>
                                </div>
                            </div>

                            <div class="mt-6 text-sm text-gray-600">
                                <p><strong>Payment Terms:</strong> Payment due within 30 days</p>
                                <p><strong>Thank you for your business!</strong></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 Calculator City.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize dates
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const dueDate = new Date(today);
            dueDate.setDate(today.getDate() + 30);

            document.getElementById('invoiceDate').value = today.toISOString().split('T')[0];
            document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];

            updateInvoice();
        });

        // Add line item
        function addLineItem() {
            const lineItems = document.getElementById('line-items');
            const newItem = document.createElement('div');
            newItem.className = 'line-item grid grid-cols-12 gap-2 mb-2';
            newItem.innerHTML = `
                <div class="col-span-6">
                    <input type="text" placeholder="Description" class="item-description" oninput="updateInvoice()">
                </div>
                <div class="col-span-2">
                    <input type="number" placeholder="Qty" value="1" class="item-quantity" oninput="updateInvoice()">
                </div>
                <div class="col-span-2">
                    <input type="number" placeholder="Rate" value="0" class="item-rate" oninput="updateInvoice()">
                </div>
                <div class="col-span-2">
                    <input type="text" placeholder="Amount" class="item-amount" readonly>
                </div>
            `;
            lineItems.appendChild(newItem);
            updateInvoice();
        }

        // Update invoice preview
        function updateInvoice() {
            // Update business details
            document.getElementById('preview-business-name').textContent = document.getElementById('businessName').value;
            document.getElementById('preview-abn').textContent = document.getElementById('abn').value;
            document.getElementById('preview-business-address').innerHTML = document.getElementById('businessAddress').value.replace(/\n/g, '<br>');

            // Update client details
            document.getElementById('preview-client-name').textContent = document.getElementById('clientName').value;
            document.getElementById('preview-client-address').innerHTML = document.getElementById('clientAddress').value.replace(/\n/g, '<br>');

            // Update invoice details
            document.getElementById('preview-invoice-number').textContent = document.getElementById('invoiceNumber').value;
            document.getElementById('preview-invoice-date').textContent = formatDate(document.getElementById('invoiceDate').value);
            document.getElementById('preview-due-date').textContent = formatDate(document.getElementById('dueDate').value);

            // Update line items and calculate totals
            const lineItems = document.querySelectorAll('.line-item');
            const previewItems = document.getElementById('preview-items');
            previewItems.innerHTML = '';

            let subtotal = 0;

            lineItems.forEach(item => {
                const description = item.querySelector('.item-description').value;
                const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
                const rate = parseFloat(item.querySelector('.item-rate').value) || 0;
                const amount = quantity * rate;

                // Update amount field
                item.querySelector('.item-amount').value = formatCurrency(amount);

                if (description) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${description}</td>
                        <td>${quantity}</td>
                        <td>${formatCurrency(rate)}</td>
                        <td>${formatCurrency(amount)}</td>
                    `;
                    previewItems.appendChild(row);
                    subtotal += amount;
                }
            });

            // Calculate GST and total
            const gstRegistered = document.getElementById('gstRegistered').value === 'yes';
            const gst = gstRegistered ? subtotal * 0.1 : 0;
            const total = subtotal + gst;

            // Update totals
            document.getElementById('preview-subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('preview-gst').textContent = formatCurrency(gst);
            document.getElementById('preview-total').textContent = formatCurrency(total);

            // Show/hide GST row
            document.getElementById('gst-row').style.display = gstRegistered ? 'flex' : 'none';
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 2
            }).format(amount);
        }

        // Format date
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-AU');
        }

        // Print invoice
        function printInvoice() {
            window.print();
        }

        // Download PDF (simplified - would need a PDF library in real implementation)
        function downloadPDF() {
            alert('PDF download feature would be implemented with a PDF library like jsPDF or similar.');
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }

        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }
    </script>
    <script src="fix-navigation.js"></script>
</body>
</html>
