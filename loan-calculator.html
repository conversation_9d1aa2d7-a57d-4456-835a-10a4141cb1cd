﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Loan Calculator - Personal Loan Payment Calculator | Financial Calculator</title>
    <meta name="description" content="Free loan calculator to calculate monthly payments, total interest, and loan costs. Compare different loan terms and interest rates to find the best personal loan option.">
    <meta name="keywords" content="loan calculator,personal loan calculator,monthly payment calculator,loan payment calculator,interest calculator,loan comparison tool,auto loan calculator,student loan calculator,debt calculator,finance calculator">
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="white">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="black">
    <meta name="creator" content="Financial Calculator">
    <link rel="canonical" href="/loan-calculator.html">
    <meta property="og:title" content="Loan Calculator - Personal Loan Payment Calculator">
    <meta property="og:description" content="Free loan calculator to calculate monthly payments, total interest, and loan costs. Compare different loan terms and interest rates to find the best personal loan option.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Loan Calculator - Personal Loan Payment Calculator">
    <meta name="twitter:description" content="Free loan calculator to calculate monthly payments, total interest, and loan costs. Compare different loan terms and interest rates to find the best personal loan option.">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Calculator City Header Component -->
        <div class="bg-blue-100/90 dark:bg-blue-900/30 border-b border-blue-200 dark:border-blue-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">💰 New!</span>
                        <span class="text-sm sm:text-base">Calculate your <a class="font-semibold text-blue-600 dark:text-blue-400 hover:underline" href="#calculator">loan payments instantly</a><span class="hidden sm:inline"> - Compare rates and save money!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-blue-300 dark:border-blue-700 hover:bg-blue-200/70 dark:hover:bg-blue-800/30" href="#calculator">Try Now</a>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-blue-300 dark:border-blue-700 hover:bg-blue-200/70 dark:hover:bg-blue-800/30" href="#calculator">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                            <span class="sr-only">Try Calculator</span>
                        </a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-blue-200/70 dark:hover:bg-blue-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <!-- Mobile Menu Button -->
                <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9 md:hidden" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="h-5 w-5">
                        <path d="M1.5 3C1.22386 3 1 3.22386 1 3.5C1 3.77614 1.22386 4 1.5 4H13.5C13.7761 4 14 3.77614 14 3.5C14 3.22386 13.7761 3 13.5 3H1.5ZM1 7.5C1 7.22386 1.22386 7 1.5 7H13.5C13.7761 7 14 7.22386 14 7.5C14 7.77614 13.7761 8 13.5 8H1.5C1.22386 8 1 7.77614 1 7.5ZM1 11.5C1 11.2239 1.22386 11 1.5 11H13.5C13.7761 11 14 11.2239 14 11.5C14 11.7761 13.7761 12 13.5 12H1.5C1.22386 12 1 11.7761 1 11.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Desktop Navigation -->
                <div class="mr-4 hidden md:flex">
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <!-- Logo -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white mr-4" aria-hidden="true">
                            <path d="M0 0h256v256H0z" fill="none"></path>
                            <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                            <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                            <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                        </svg>

                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Calculator Suite
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Health Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="bmi-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">BMI Calculator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Body Fat Calculator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Calorie Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="loan-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Loan Calculator</a>
                                                <a href="investment-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Investment Calculator</a>
                                                <a href="retirement-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Retirement Calculator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Savings Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#about">
                                        About
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>

                <!-- Right Side Actions -->
                <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                    <div class="flex flex-1 items-center justify-end space-x-4">
                        <nav class="flex items-center space-x-1">
                            <!-- Theme Toggle -->
                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                    <circle cx="12" cy="12" r="4"></circle>
                                    <path d="M12 2v2"></path>
                                    <path d="M12 20v2"></path>
                                    <path d="m4.93 4.93 1.41 1.41"></path>
                                    <path d="m17.66 17.66 1.41 1.41"></path>
                                    <path d="M2 12h2"></path>
                                    <path d="M20 12h2"></path>
                                    <path d="m6.34 17.66-1.41 1.41"></path>
                                    <path d="m19.07 4.93-1.41 1.41"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                </svg>
                                <span class="sr-only">Toggle theme</span>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden border-t bg-background">
                <div class="container px-4 py-4">
                    <nav class="space-y-2">
                        <a href="index.html" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Calculator Suite</a>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Health Calculators</div>
                            <a href="bmi-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">BMI Calculator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Body Fat Calculator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Calorie Calculator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Financial Calculators</div>
                            <a href="loan-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Loan Calculator</a>
                            <a href="investment-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Investment Calculator</a>
                            <a href="retirement-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Retirement Calculator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Savings Calculator</a>
                        </div>
                        <a href="#about" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">About</a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="flex-1">
            <main class="flex flex-col items-center justify-between">
                <div class="container relative">
                    <div class="flex flex-col md:flex-row md:gap-8">
                        <div class="flex-1">
                            <section class="flex flex-col items-start gap-2 px-4 pt-8 md:pt-12 pb-8">
                                <a class="group inline-flex items-center rounded-lg text-sm font-medium transition-colors mb-6 hover:text-primary" href="#loan-tips">
                                    <span class="mr-2">🏦</span>Looking for loan tips? Check our Loan Guide
                                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4 group-hover:translate-x-0.5 transition-transform">
                                        <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <h1 class="text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:leading-[1.1]">Loan Payment Calculator</h1>
                                <span class="max-w-[750px] text-lg text-muted-foreground sm:text-xl">
                                    Calculate your monthly loan payments and total interest costs to make informed borrowing decisions
                                </span>
                            </section>
                        </div>
                    </div>

                    <!-- Calculator Section -->
                    <section id="calculator" class="px-4 py-8">
                        <div class="flex flex-wrap justify-center items-center w-full gap-4 flex-grow overflow-hidden rounded-[0.5rem] p-4 sm:p-8 border bg-background shadow">
                            <div class="flex flex-col md:flex-row gap-4 w-full max-w-screen-xl">
                                <!-- Calculator Input Panel -->
                                <div class="md:w-1/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Loan Details</h3>
                                            <p class="text-sm text-muted-foreground">Enter your loan information</p>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <form class="grid gap-6" id="loanForm">
                                                <!-- Loan Amount -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="loanAmount">Loan Amount</label>
                                                    <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                        <span class="flex items-center px-3 h-10">$</span>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="loanAmount" placeholder="Enter loan amount" value="10000" min="100" max="1000000">
                                                    </div>
                                                </div>

                                                <!-- Interest Rate -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="interestRate">Annual Interest Rate (%)</label>
                                                    <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="interestRate" placeholder="Enter interest rate" value="8.5" min="0" max="50" step="0.01">
                                                </div>

                                                <!-- Loan Term -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="loanTerm">Loan Term</label>
                                                    <div class="flex gap-2">
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="loanTerm" placeholder="Term" value="5" min="1" max="50">
                                                        <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="termUnit">
                                                            <option value="years">Years</option>
                                                            <option value="months">Months</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Payment Frequency -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="paymentFrequency">Payment Frequency</label>
                                                    <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="paymentFrequency">
                                                        <option value="monthly">Monthly</option>
                                                        <option value="fortnightly">Fortnightly</option>
                                                        <option value="weekly">Weekly</option>
                                                    </select>
                                                </div>

                                                <!-- Loan Type -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="loanType">Loan Type</label>
                                                    <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="loanType">
                                                        <option value="personal">Personal Loan</option>
                                                        <option value="auto">Auto Loan</option>
                                                        <option value="student">Student Loan</option>
                                                        <option value="business">Business Loan</option>
                                                        <option value="other">Other</option>
                                                    </select>
                                                </div>

                                                <!-- Calculate Button -->
                                                <button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2" onclick="calculateLoan()">
                                                    Calculate Loan
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Results Panel -->
                                <div class="md:w-2/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Loan Summary</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="loanResults" class="w-full overflow-auto">
                                                <div class="text-center text-muted-foreground py-8">
                                                    Enter your loan details and click Calculate Loan to see results
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Breakdown Chart -->
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Breakdown</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="loanChart" class="w-full h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                                                <canvas id="loanCanvas" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Educational Content Section -->
                    <section id="about" class="px-4 py-8">
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 w-full flex-1 flex flex-col">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <h3 class="text-2xl font-semibold leading-none tracking-tight">Understanding Personal Loans</h3>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="container mx-auto p-6">
                                    <div class="grid md:grid-cols-2 gap-6">
                                        <!-- Loan Image -->
                                        <div class="space-y-4">
                                            <div class="bg-gradient-to-br from-blue-100 to-purple-100 p-6 rounded-lg">
                                                <div class="w-full h-48 bg-gradient-to-r from-blue-200 to-purple-200 rounded-lg flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                                        <rect width="20" height="14" x="2" y="5" rx="2"/>
                                                        <line x1="2" x2="22" y1="10" y2="10"/>
                                                    </svg>
                                                </div>
                                                <h4 class="text-lg font-semibold mt-4 text-blue-800">Smart Borrowing</h4>
                                                <p class="text-blue-700 text-sm">Calculate loan payments and compare options to make informed financial decisions.</p>
                                            </div>
                                        </div>

                                        <!-- Content -->
                                        <div class="space-y-4">
                                            <h4 class="text-xl font-semibold">What is a Personal Loan?</h4>
                                            <p class="text-lg mb-4">A personal loan is an unsecured loan that you can use for various purposes such as debt consolidation, home improvements, or major purchases. Unlike secured loans, personal loans don't require collateral.</p>

                                            <h4 class="text-lg font-medium mt-4 mb-2">Key Loan Terms:</h4>
                                            <ul class="list-disc pl-5 mb-4 space-y-2">
                                                <li class="mb-2"><strong>Principal:</strong> The original loan amount</li>
                                                <li class="mb-2"><strong>Interest Rate:</strong> The cost of borrowing money</li>
                                                <li class="mb-2"><strong>Term:</strong> The length of time to repay the loan</li>
                                                <li class="mb-2"><strong>APR:</strong> Annual Percentage Rate including fees</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Loan Types Grid -->
                                    <div class="mt-8 grid md:grid-cols-3 gap-4">
                                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600 mr-2">
                                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                                    <circle cx="9" cy="7" r="4"/>
                                                    <path d="m22 2-5 10-5-5 10-5z"/>
                                                </svg>
                                                <h5 class="font-semibold text-green-800">Personal Loans</h5>
                                            </div>
                                            <p class="text-sm text-green-700">Flexible loans for personal expenses, debt consolidation, or major purchases.</p>
                                        </div>

                                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mr-2">
                                                    <path d="M14 16V8a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2Z"/>
                                                    <path d="M15 2H9"/>
                                                    <path d="M15 22H9"/>
                                                    <path d="M2 15V9"/>
                                                    <path d="M22 15V9"/>
                                                </svg>
                                                <h5 class="font-semibold text-blue-800">Auto Loans</h5>
                                            </div>
                                            <p class="text-sm text-blue-700">Secured loans specifically for purchasing vehicles with competitive rates.</p>
                                        </div>

                                        <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-purple-600 mr-2">
                                                    <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
                                                    <path d="M6 12v5c3 0 5-1 7-2"/>
                                                </svg>
                                                <h5 class="font-semibold text-purple-800">Student Loans</h5>
                                            </div>
                                            <p class="text-sm text-purple-700">Education financing with flexible repayment options and potential benefits.</p>
                                        </div>
                                    </div>

                                    <!-- Loan Tips -->
                                    <div class="mt-8 bg-yellow-50 p-6 rounded-lg border border-yellow-200" id="loan-tips">
                                        <h4 class="text-lg font-semibold text-yellow-800 mb-3">💡 Smart Borrowing Tips</h4>
                                        <div class="grid md:grid-cols-2 gap-4 text-sm text-yellow-700">
                                            <div>
                                                <p class="mb-2"><strong>Shop Around:</strong> Compare rates from multiple lenders to find the best deal.</p>
                                                <p class="mb-2"><strong>Check Your Credit:</strong> Better credit scores typically qualify for lower interest rates.</p>
                                            </div>
                                            <div>
                                                <p class="mb-2"><strong>Consider the Total Cost:</strong> Look at total interest paid, not just monthly payments.</p>
                                                <p class="mb-2"><strong>Read the Fine Print:</strong> Understand all fees, penalties, and terms before signing.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Important Note -->
                                    <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                        <p class="text-sm text-red-800">
                                            <strong>Important:</strong> This calculator provides estimates only. Actual loan terms, rates, and payments may vary based on your creditworthiness,
                                            lender policies, and market conditions. Always consult with financial advisors and compare multiple loan offers before making decisions.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>

        <!-- Calculator City Footer Component -->
        <footer class="border-t mt-24">
            <div class="container mx-auto px-6 py-12">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                    <!-- Brand Section -->
                    <div class="lg:col-span-2">
                        <a class="text-xl font-bold" href="index.html">Calculator Suite</a>
                        <p class="mt-4 text-sm text-muted-foreground">
                            Professional calculators for health, finance, and planning. Helping you make informed decisions with accurate calculations and educational content.
                        </p>
                        <div class="mt-6">
                            <a href="mailto:<EMAIL>" class="text-sm text-muted-foreground hover:text-primary">
                                <EMAIL>
                            </a>
                        </div>
                    </div>

                    <!-- Health Calculators -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Health Calculators</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="bmi-calculator.html">BMI Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Body Fat Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Calorie Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Heart Rate Calculator</a></li>
                        </ul>
                    </div>

                    <!-- Financial Calculators -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Financial Calculators</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="loan-calculator.html">Loan Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="investment-calculator.html">Investment Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="retirement-calculator.html">Retirement Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Mortgage Calculator</a></li>
                        </ul>
                    </div>

                    <!-- Resources -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Resources</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#about">About Us</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#contact">Contact</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#privacy">Privacy Policy</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#terms">Terms of Service</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Footer Bottom -->
                <div class="mt-12 pt-8 border-t">
                    <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-4">
                            <a class="text-sm text-muted-foreground hover:text-primary" href="#privacy">Privacy Policy</a>
                            <span class="text-muted-foreground">•</span>
                            <a class="text-sm text-muted-foreground hover:text-primary" href="#terms">Terms</a>
                            <span class="text-muted-foreground">•</span>
                            <a class="text-sm text-muted-foreground hover:text-primary" href="#contact">Contact</a>
                        </div>
                        <p class="text-sm text-muted-foreground">
                            Made with ❤️ © 2025 Calculator Suite
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script src="loan-calculator.js"></script>
    <script>
    // Mobile menu toggle
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenu.classList.toggle('hidden');
    }

    // Dismiss banner
    function dismissBanner() {
        const banner = document.querySelector('.bg-blue-100\\/90');
        if (banner) {
            banner.style.display = 'none';
        }
    }
    </script>
</body>
</html>
