// Mortgage Calculator JavaScript
let mortgageChart = null;

// Calculate monthly mortgage payment using the standard formula
function calculateMortgagePayment(principal, annualRate, years, extraPayment = 0) {
    const monthlyRate = annualRate / 100 / 12;
    const numberOfPayments = years * 12;
    
    if (monthlyRate === 0) {
        return principal / numberOfPayments;
    }
    
    const monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
                          (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    
    return monthlyPayment + extraPayment;
}

// Calculate total interest and loan details
function calculateLoanDetails(principal, annualRate, years, extraPayment = 0) {
    const monthlyRate = annualRate / 100 / 12;
    const numberOfPayments = years * 12;
    
    // Calculate base monthly payment (without extra payment)
    let baseMonthlyPayment;
    if (monthlyRate === 0) {
        baseMonthlyPayment = principal / numberOfPayments;
    } else {
        baseMonthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
                            (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    }
    
    const totalMonthlyPayment = baseMonthlyPayment + extraPayment;
    
    // Calculate actual payoff time with extra payments
    let balance = principal;
    let totalInterest = 0;
    let monthsPaid = 0;
    const maxMonths = years * 12;
    
    while (balance > 0.01 && monthsPaid < maxMonths * 2) { // Safety limit
        const interestPayment = balance * monthlyRate;
        const principalPayment = Math.min(totalMonthlyPayment - interestPayment, balance);
        
        if (principalPayment <= 0) break; // Safety check
        
        balance -= principalPayment;
        totalInterest += interestPayment;
        monthsPaid++;
    }
    
    const actualYears = monthsPaid / 12;
    const totalAmount = principal + totalInterest;
    
    return {
        monthlyPayment: totalMonthlyPayment,
        baseMonthlyPayment: baseMonthlyPayment,
        totalInterest: totalInterest,
        totalAmount: totalAmount,
        actualTerm: actualYears,
        monthsPaid: monthsPaid
    };
}

// Generate amortization schedule for chart
function generateAmortizationData(principal, annualRate, years, extraPayment = 0) {
    const monthlyRate = annualRate / 100 / 12;
    let balance = principal;
    const data = [];
    let month = 0;
    
    // Calculate base monthly payment
    let baseMonthlyPayment;
    if (monthlyRate === 0) {
        baseMonthlyPayment = principal / (years * 12);
    } else {
        baseMonthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, years * 12)) / 
                            (Math.pow(1 + monthlyRate, years * 12) - 1);
    }
    
    const totalMonthlyPayment = baseMonthlyPayment + extraPayment;
    
    while (balance > 0.01 && month < years * 12 * 2) { // Safety limit
        const interestPayment = balance * monthlyRate;
        const principalPayment = Math.min(totalMonthlyPayment - interestPayment, balance);
        
        if (principalPayment <= 0) break;
        
        balance -= principalPayment;
        month++;
        
        // Store data for every 12 months (yearly)
        if (month % 12 === 0 || balance <= 0.01) {
            data.push({
                year: Math.ceil(month / 12),
                principal: principal - balance,
                interest: (month * totalMonthlyPayment) - (principal - balance),
                balance: balance
            });
        }
    }
    
    return data;
}

// Update the mortgage chart
function updateMortgageChart(principal, annualRate, years, extraPayment = 0) {
    const ctx = document.getElementById('mortgage-chart');
    if (!ctx) return;
    
    const data = generateAmortizationData(principal, annualRate, years, extraPayment);
    
    if (mortgageChart) {
        mortgageChart.destroy();
    }
    
    mortgageChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(d => `Year ${d.year}`),
            datasets: [
                {
                    label: 'Principal Paid',
                    data: data.map(d => d.principal),
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Interest Paid',
                    data: data.map(d => d.interest),
                    backgroundColor: 'rgba(239, 68, 68, 0.8)',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Years'
                    }
                },
                y: {
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Amount ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'Principal vs Interest Over Time'
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

// Format years
function formatYears(years) {
    const wholeYears = Math.floor(years);
    const months = Math.round((years - wholeYears) * 12);
    
    if (months === 0) {
        return `${wholeYears} year${wholeYears !== 1 ? 's' : ''}`;
    } else if (wholeYears === 0) {
        return `${months} month${months !== 1 ? 's' : ''}`;
    } else {
        return `${wholeYears} year${wholeYears !== 1 ? 's' : ''} ${months} month${months !== 1 ? 's' : ''}`;
    }
}

// Main calculation function
function calculateMortgage() {
    // Get input values
    const loanAmount = parseFloat(document.getElementById('loan-amount')?.value) || 500000;
    const interestRate = parseFloat(document.getElementById('interest-rate')?.value) || 6.5;
    const loanTerm = parseFloat(document.getElementById('loan-term')?.value) || 30;
    const extraPayment = parseFloat(document.getElementById('extra-payment')?.value) || 0;
    
    // Validate inputs
    if (loanAmount <= 0 || interestRate < 0 || loanTerm <= 0) {
        return;
    }
    
    // Calculate loan details
    const loanDetails = calculateLoanDetails(loanAmount, interestRate, loanTerm, extraPayment);
    
    // Update results
    const monthlyPaymentElement = document.getElementById('monthly-payment');
    const totalInterestElement = document.getElementById('total-interest');
    const totalAmountElement = document.getElementById('total-amount');
    const actualTermElement = document.getElementById('actual-term');
    
    if (monthlyPaymentElement) {
        monthlyPaymentElement.textContent = formatCurrency(loanDetails.monthlyPayment);
    }
    
    if (totalInterestElement) {
        totalInterestElement.textContent = formatCurrency(loanDetails.totalInterest);
    }
    
    if (totalAmountElement) {
        totalAmountElement.textContent = formatCurrency(loanDetails.totalAmount);
    }
    
    if (actualTermElement) {
        actualTermElement.textContent = formatYears(loanDetails.actualTerm);
    }
    
    // Update chart
    updateMortgageChart(loanAmount, interestRate, loanTerm, extraPayment);
}

// Initialize the calculator
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all input fields
    const inputs = ['loan-amount', 'interest-rate', 'loan-term', 'extra-payment'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', calculateMortgage);
            element.addEventListener('change', calculateMortgage);
        }
    });
    
    // Initial calculation
    calculateMortgage();
});
