﻿<!-- Navigation Bar Template -->
<header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
    <div class="container flex h-14 items-center">
        <div class="mr-4 hidden md:flex">
            <a class="mr-6 flex items-center space-x-2" href="index.html">
                <span class="hidden font-bold sm:inline-block">Calculator City</span>
            </a>
            <nav class="flex items-center space-x-6 text-sm font-medium">
                <div style="position:relative">
                    <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                Calculator City.com.au
                            </a>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Pay Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                    <div class="p-2">
                                        <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                        <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                        <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                        <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Home & Property Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                    <div class="p-2">
                                        <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                        <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                        <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                        <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Tax Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                    <div class="p-2">
                                        <a href="salary-tax-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Tax Calculator</a>
                                        <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                        <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                        <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Financial Tools
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                    <div class="p-2">
                                        <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                        <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                        <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Vehicle Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                    <div class="p-2">
                                        <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                        <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Holidays Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                    <div class="p-2">
                                        <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                        <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                Recommended
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <!-- Mobile menu button -->
        <button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:R2qla:" data-state="closed" onclick="toggleMobileMenu()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                <line x1="4" x2="20" y1="12" y2="12"></line>
                <line x1="4" x2="20" y1="6" y2="6"></line>
                <line x1="4" x2="20" y1="18" y2="18"></line>
            </svg>
            <span class="sr-only">Toggle Menu</span>
        </button>
        
        <!-- Mobile menu -->
        <div class="fixed left-0 top-0 z-50 grid h-[calc(100vh-4rem)] w-full translate-x-[-100%] grid-flow-row auto-rows-max overflow-auto p-6 pb-32 shadow-md animate-in slide-in-from-bottom-80 md:hidden hidden" id="mobile-menu" style="--radix-navigation-menu-viewport-width: 0px; --radix-navigation-menu-viewport-height: 0px;">
            <div class="relative z-20 grid gap-6 rounded-md bg-popover p-4 text-popover-foreground shadow-md">
                <a class="flex items-center space-x-2" href="index.html">
                    <span class="font-bold">Calculator City</span>
                </a>
                <nav class="grid gap-2">
                    <a href="index.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                        <div class="text-sm font-medium leading-none">Australian Pay Calculator</div>
                        <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate salary, tax and superannuation</p>
                    </a>
                    <a href="contractor-pay-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                        <div class="text-sm font-medium leading-none">Contractor Pay Calculator</div>
                        <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate contractor rates and take-home pay</p>
                    </a>
                    <a href="mortgage-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                        <div class="text-sm font-medium leading-none">Mortgage Calculator</div>
                        <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate home loan repayments</p>
                    </a>
                    <a href="novated-lease-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                        <div class="text-sm font-medium leading-none">Novated Lease Calculator</div>
                        <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Compare EV vs ICE vehicle costs</p>
                    </a>
                </nav>
            </div>
        </div>
    </div>
</header>
