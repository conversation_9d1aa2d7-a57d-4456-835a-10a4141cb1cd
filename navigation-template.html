<!-- PayCal Australia Navigation Template -->
<header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
    <div class="container flex h-14 items-center">
        <!-- Mobile Menu Button -->
        <button class="inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-10 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" onclick="toggleMobileMenu()">
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5">
                <path d="M8 2H13.5C13.7761 2 14 2.22386 14 2.5V12.5C14 12.7761 13.7761 13 13.5 13H8V2ZM7 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H7V2ZM0 2.5C0 1.67157 0.671573 1 1.5 1H13.5C14.3284 1 15 1.67157 15 2.5V12.5C15 13.3284 14.3284 14 13.5 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
            </svg>
            <span class="sr-only">Toggle Menu</span>
        </button>
        
        <!-- Desktop Navigation -->
        <div class="mr-4 hidden md:flex">
            <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                <!-- PayCal Logo -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                    <path d="M0 0h256v256H0z" fill="none"></path>
                    <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                    <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                    <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                </svg>

                <div style="position:relative">
                    <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                Paycal.com.au
                            </a>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Pay Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                    <div class="p-2">
                                        <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                        <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                        <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                        <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Home & Property Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                    <div class="p-2">
                                        <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                        <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                        <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                        <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Tax Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                        <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                        <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Financial Tools
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                        <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                        <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Vehicle Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                    <div class="p-2">
                                        <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                        <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Holidays Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                        <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                Recommended
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="absolute left-0 top-full flex justify-center"></div>
            </nav>
        </div>

        <!-- Right Side Actions -->
        <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
            <div class="flex flex-1 items-center justify-end space-x-4">
                <nav class="flex items-center space-x-1">
                    <!-- Theme Toggle -->
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                            <circle cx="12" cy="12" r="4"></circle>
                            <path d="M12 2v2"></path>
                            <path d="M12 20v2"></path>
                            <path d="m4.93 4.93 1.41 1.41"></path>
                            <path d="m17.66 17.66 1.41 1.41"></path>
                            <path d="M2 12h2"></path>
                            <path d="M20 12h2"></path>
                            <path d="m6.34 17.66-1.41 1.41"></path>
                            <path d="m19.07 4.93-1.41 1.41"></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                        </svg>
                        <span class="sr-only">Toggle theme</span>
                    </button>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden md:hidden border-t bg-background">
        <div class="container px-4 py-4">
            <nav class="space-y-2">
                <a href="index.html" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Paycal.com.au</a>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Pay Calculators</div>
                    <a href="index.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                    <a href="contractor-pay-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                    <a href="contract-rate-benchmarking.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                    <a href="salary-rate-benchmarking.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Home & Property Calculators</div>
                    <a href="mortgage-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                    <a href="home-loan-comparison.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                    <a href="rate-cut-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                    <a href="mortgage-payoff-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Tax Calculators</div>
                    <a href="tax-cut-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                    <a href="tax-calendar.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                    <a href="gst-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Financial Tools</div>
                    <a href="invoice-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                    <a href="quote-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                    <a href="payslip-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Vehicle Calculators</div>
                    <a href="novated-lease-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                    <a href="vehicle-registration.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Holidays Calculators</div>
                    <a href="public-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                    <a href="school-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                </div>
                <a href="#recommended" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Recommended</a>
            </nav>
        </div>
    </div>
</header>

<script>
// Mobile menu toggle
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('hidden');
    }
}

// Theme toggle
function toggleTheme() {
    const html = document.documentElement;
    const isDark = html.classList.contains('dark');
    
    if (isDark) {
        html.classList.remove('dark');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    } else {
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    }
}

// Initialize theme
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add('dark');
        document.documentElement.style.colorScheme = 'dark';
    } else {
        document.documentElement.classList.remove('dark');
        document.documentElement.style.colorScheme = 'light';
    }
}

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', initializeTheme);
</script>
