<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Payslip Generator | Australian Payslip Template | PayCal Australia</title>
    <meta name="description" content="Generate professional Australian payslips for your employees. Include tax, superannuation, and all required deductions. Download as PDF instantly.">
    <meta name="keywords" content="payslip generator,australian payslip,payslip template,employee payslip,salary slip,pay stub generator,free payslip,payroll">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-secondary {
            background: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
            padding: 0.75rem 1.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .payslip-preview {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: var(--radius);
            padding: 2rem;
            font-family: 'Arial', sans-serif;
            color: #000;
        }

        .payslip-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #000;
        }

        .payslip-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .payslip-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .payslip-table th,
        .payslip-table td {
            padding: 0.75rem;
            text-align: left;
            border: 1px solid #e5e7eb;
        }

        .payslip-table th {
            background: #f9fafb;
            font-weight: 600;
        }

        .payslip-totals {
            background: #f9fafb;
            padding: 1rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
        }

        .total-row.final {
            border-top: 2px solid #000;
            font-weight: bold;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .payslip-preview {
                padding: 1rem;
            }
            .payslip-details {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Navigation Bar -->
        <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <a class="mr-6 flex items-center space-x-2" href="index.html">
                        <span class="hidden font-bold sm:inline-block">PayCal Australia</span>
                    </a>
                    <nav class="flex items-center space-x-6 text-sm font-medium">
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Paycal.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="salary-tax-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Tax Calculator</a>
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>

                <!-- Mobile menu button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Mobile menu -->
                <div class="fixed left-0 top-0 z-50 grid h-[calc(100vh-4rem)] w-full translate-x-[-100%] grid-flow-row auto-rows-max overflow-auto p-6 pb-32 shadow-md animate-in slide-in-from-bottom-80 md:hidden hidden" id="mobile-menu">
                    <div class="relative z-20 grid gap-6 rounded-md bg-popover p-4 text-popover-foreground shadow-md">
                        <a class="flex items-center space-x-2" href="index.html">
                            <span class="font-bold">PayCal Australia</span>
                        </a>
                        <nav class="grid gap-2">
                            <a href="index.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Australian Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate salary, tax and superannuation</p>
                            </a>
                            <a href="contractor-pay-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Contractor Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate contractor rates and take-home pay</p>
                            </a>
                            <a href="mortgage-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Mortgage Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate home loan repayments</p>
                            </a>
                            <a href="novated-lease-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Novated Lease Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Compare EV vs ICE vehicle costs</p>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- PayCal Australia Header -->
        <div class="bg-orange-100/90 border-b border-orange-200 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">💰 Free!</span>
                        <span class="text-sm sm:text-base">Generate professional <a class="font-semibold text-orange-600 hover:underline" href="#generator">Australian payslips</a><span class="hidden sm:inline"> - Download instantly!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 hover:bg-orange-200/70" href="#generator">Create Payslip</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Free Payslip Generator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Generate professional Australian payslips for your employees. Include tax, superannuation, and all required deductions according to Australian standards.
                    </p>
                </div>

                <!-- Payslip Generator -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto" id="generator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Payslip Details</h2>
                        
                        <form class="grid gap-4" id="payslip-form">
                            <!-- Employer Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Employer Details</h3>
                                <div class="grid gap-3">
                                    <div class="input-group">
                                        <label for="companyName">Company Name</label>
                                        <input type="text" id="companyName" placeholder="Company Name" value="ABC Company Pty Ltd" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="companyABN">ABN</label>
                                        <input type="text" id="companyABN" placeholder="**************" value="**************" oninput="updatePayslip()">
                                    </div>
                                </div>
                            </div>

                            <!-- Employee Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Employee Details</h3>
                                <div class="grid gap-3">
                                    <div class="input-group">
                                        <label for="employeeName">Employee Name</label>
                                        <input type="text" id="employeeName" placeholder="Employee Name" value="John Smith" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="employeeID">Employee ID</label>
                                        <input type="text" id="employeeID" placeholder="EMP001" value="EMP001" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="tfn">Tax File Number</label>
                                        <input type="text" id="tfn" placeholder="***********" value="***********" oninput="updatePayslip()">
                                    </div>
                                </div>
                            </div>

                            <!-- Pay Period -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Pay Period</h3>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="input-group">
                                        <label for="payPeriodStart">Pay Period Start</label>
                                        <input type="date" id="payPeriodStart" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="payPeriodEnd">Pay Period End</label>
                                        <input type="date" id="payPeriodEnd" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="payDate">Pay Date</label>
                                        <input type="date" id="payDate" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="payFrequency">Pay Frequency</label>
                                        <select id="payFrequency" onchange="updatePayslip()">
                                            <option value="Weekly">Weekly</option>
                                            <option value="Fortnightly" selected>Fortnightly</option>
                                            <option value="Monthly">Monthly</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Earnings -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Earnings</h3>
                                <div class="grid gap-3">
                                    <div class="input-group">
                                        <label for="grossSalary">Gross Salary</label>
                                        <input type="number" id="grossSalary" placeholder="3000" value="3000" step="0.01" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="overtimeHours">Overtime Hours</label>
                                        <input type="number" id="overtimeHours" placeholder="0" value="0" step="0.1" oninput="updatePayslip()">
                                    </div>
                                    <div class="input-group">
                                        <label for="overtimeRate">Overtime Rate (per hour)</label>
                                        <input type="number" id="overtimeRate" placeholder="45" value="45" step="0.01" oninput="updatePayslip()">
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex gap-2 pt-4">
                                <button type="button" class="btn-primary" onclick="downloadPayslipPDF()">Download PDF</button>
                                <button type="button" class="btn-secondary" onclick="printPayslip()">Print</button>
                            </div>
                        </form>
                    </div>

                    <!-- Payslip Preview -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Payslip Preview</h2>
                        
                        <div class="payslip-preview" id="payslip-preview">
                            <div class="payslip-header">
                                <h1 class="text-2xl font-bold" id="preview-company-name">ABC Company Pty Ltd</h1>
                                <p class="text-sm">ABN: <span id="preview-company-abn">**************</span></p>
                                <h2 class="text-xl font-semibold mt-4">PAYSLIP</h2>
                            </div>

                            <div class="payslip-details">
                                <div>
                                    <h3 class="font-semibold mb-2">Employee Details</h3>
                                    <p><strong>Name:</strong> <span id="preview-employee-name">John Smith</span></p>
                                    <p><strong>Employee ID:</strong> <span id="preview-employee-id">EMP001</span></p>
                                    <p><strong>TFN:</strong> <span id="preview-tfn">***********</span></p>
                                </div>
                                <div>
                                    <h3 class="font-semibold mb-2">Pay Period</h3>
                                    <p><strong>Period:</strong> <span id="preview-pay-period">01/01/2024 - 14/01/2024</span></p>
                                    <p><strong>Pay Date:</strong> <span id="preview-pay-date">15/01/2024</span></p>
                                    <p><strong>Frequency:</strong> <span id="preview-pay-frequency">Fortnightly</span></p>
                                </div>
                            </div>

                            <table class="payslip-table">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Hours/Units</th>
                                        <th>Rate</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Gross Salary</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td id="preview-gross-salary">$3,000.00</td>
                                    </tr>
                                    <tr id="overtime-row" style="display: none;">
                                        <td>Overtime</td>
                                        <td id="preview-overtime-hours">0</td>
                                        <td id="preview-overtime-rate">$45.00</td>
                                        <td id="preview-overtime-amount">$0.00</td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="payslip-totals">
                                <div class="total-row">
                                    <span>Gross Earnings:</span>
                                    <span id="preview-total-gross">$3,000.00</span>
                                </div>
                                <div class="total-row">
                                    <span>Income Tax:</span>
                                    <span id="preview-income-tax">-$462.00</span>
                                </div>
                                <div class="total-row">
                                    <span>Medicare Levy:</span>
                                    <span id="preview-medicare">-$60.00</span>
                                </div>
                                <div class="total-row">
                                    <span>Superannuation (11.5%):</span>
                                    <span id="preview-super">$345.00</span>
                                </div>
                                <div class="total-row final">
                                    <span>Net Pay:</span>
                                    <span id="preview-net-pay">$2,478.00</span>
                                </div>
                            </div>

                            <div class="mt-6 text-sm text-gray-600">
                                <p><strong>Year to Date Summary:</strong></p>
                                <p>Gross Earnings YTD: <span id="preview-ytd-gross">$3,000.00</span></p>
                                <p>Tax Withheld YTD: <span id="preview-ytd-tax">$462.00</span></p>
                                <p>Super Paid YTD: <span id="preview-ytd-super">$345.00</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 PayCal.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize dates
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const startDate = new Date(today);
            startDate.setDate(today.getDate() - 13); // 2 weeks ago

            document.getElementById('payPeriodStart').value = startDate.toISOString().split('T')[0];
            document.getElementById('payPeriodEnd').value = today.toISOString().split('T')[0];
            document.getElementById('payDate').value = today.toISOString().split('T')[0];

            updatePayslip();
        });

        // Update payslip preview
        function updatePayslip() {
            // Update company details
            document.getElementById('preview-company-name').textContent = document.getElementById('companyName').value;
            document.getElementById('preview-company-abn').textContent = document.getElementById('companyABN').value;

            // Update employee details
            document.getElementById('preview-employee-name').textContent = document.getElementById('employeeName').value;
            document.getElementById('preview-employee-id').textContent = document.getElementById('employeeID').value;
            document.getElementById('preview-tfn').textContent = document.getElementById('tfn').value;

            // Update pay period
            const startDate = formatDate(document.getElementById('payPeriodStart').value);
            const endDate = formatDate(document.getElementById('payPeriodEnd').value);
            document.getElementById('preview-pay-period').textContent = `${startDate} - ${endDate}`;
            document.getElementById('preview-pay-date').textContent = formatDate(document.getElementById('payDate').value);
            document.getElementById('preview-pay-frequency').textContent = document.getElementById('payFrequency').value;

            // Calculate earnings
            const grossSalary = parseFloat(document.getElementById('grossSalary').value) || 0;
            const overtimeHours = parseFloat(document.getElementById('overtimeHours').value) || 0;
            const overtimeRate = parseFloat(document.getElementById('overtimeRate').value) || 0;
            const overtimeAmount = overtimeHours * overtimeRate;
            const totalGross = grossSalary + overtimeAmount;

            // Update earnings display
            document.getElementById('preview-gross-salary').textContent = formatCurrency(grossSalary);
            document.getElementById('preview-overtime-hours').textContent = overtimeHours;
            document.getElementById('preview-overtime-rate').textContent = formatCurrency(overtimeRate);
            document.getElementById('preview-overtime-amount').textContent = formatCurrency(overtimeAmount);
            document.getElementById('preview-total-gross').textContent = formatCurrency(totalGross);

            // Show/hide overtime row
            document.getElementById('overtime-row').style.display = overtimeHours > 0 ? 'table-row' : 'none';

            // Calculate deductions (simplified Australian tax calculation)
            const incomeTax = calculateIncomeTax(totalGross);
            const medicareLevy = totalGross > 936 ? totalGross * 0.02 : 0; // Simplified Medicare levy (fortnightly threshold)
            const superannuation = totalGross * 0.115; // 11.5% super guarantee
            const netPay = totalGross - incomeTax - medicareLevy;

            // Update deductions display
            document.getElementById('preview-income-tax').textContent = '-' + formatCurrency(incomeTax);
            document.getElementById('preview-medicare').textContent = '-' + formatCurrency(medicareLevy);
            document.getElementById('preview-super').textContent = formatCurrency(superannuation);
            document.getElementById('preview-net-pay').textContent = formatCurrency(netPay);

            // Update YTD (simplified - assuming this is first pay)
            document.getElementById('preview-ytd-gross').textContent = formatCurrency(totalGross);
            document.getElementById('preview-ytd-tax').textContent = formatCurrency(incomeTax);
            document.getElementById('preview-ytd-super').textContent = formatCurrency(superannuation);
        }

        // Simplified Australian tax calculation for fortnightly pay
        function calculateIncomeTax(fortnightlyGross) {
            const annualGross = fortnightlyGross * 26; // Convert to annual
            let annualTax = 0;

            // 2024-25 tax brackets
            if (annualGross > 18200) {
                if (annualGross <= 45000) {
                    annualTax = (annualGross - 18200) * 0.19;
                } else if (annualGross <= 120000) {
                    annualTax = 5092 + (annualGross - 45000) * 0.325;
                } else if (annualGross <= 180000) {
                    annualTax = 29467 + (annualGross - 120000) * 0.37;
                } else {
                    annualTax = 51667 + (annualGross - 180000) * 0.45;
                }
            }

            return annualTax / 26; // Convert back to fortnightly
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 2
            }).format(amount);
        }

        // Format date
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-AU');
        }

        // Print payslip
        function printPayslip() {
            window.print();
        }

        // Download PDF (simplified - would need a PDF library in real implementation)
        function downloadPayslipPDF() {
            alert('PDF download feature would be implemented with a PDF library like jsPDF or similar.');
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }

        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }
    </script>
    <script src="fix-navigation.js"></script>
</body>
</html>
