<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian Public Holidays 2025 | Holiday Calendar | PayCal Australia</title>
    <meta name="description" content="Complete list of Australian public holidays for 2025. View national and state-specific public holidays, long weekends, and holiday dates for all Australian states and territories.">
    <meta name="keywords" content="australian public holidays,public holidays 2025,australia holiday calendar,national public holidays,state public holidays,long weekends australia,holiday dates australia">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .holiday-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .holiday-table th,
        .holiday-table td {
            border: 1px solid hsl(var(--border));
            padding: 0.75rem;
            text-align: left;
        }

        .holiday-table th {
            background: hsl(var(--muted));
            font-weight: 600;
        }

        .holiday-table .national {
            background: #dcfce7;
        }

        .holiday-table .state {
            background: #fef3c7;
        }

        .holiday-table .long-weekend {
            background: #dbeafe;
        }

        .state-filter {
            margin-bottom: 1rem;
        }

        .state-filter select {
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .legend {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .legend-color {
            width: 1rem;
            height: 1rem;
            border-radius: 0.25rem;
            border: 1px solid hsl(var(--border));
        }

        .legend-color.national {
            background: #dcfce7;
        }

        .legend-color.state {
            background: #fef3c7;
        }

        .legend-color.long-weekend {
            background: #dbeafe;
        }

        .upcoming-holidays {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-bottom: 1rem;
        }

        .upcoming-holidays h3 {
            margin: 0 0 1rem 0;
            color: hsl(var(--foreground));
        }

        .next-holiday {
            background: #dcfce7;
            border: 2px solid #16a34a;
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .next-holiday h4 {
            color: #166534;
            margin: 0 0 0.5rem 0;
            font-weight: 600;
        }

        .days-until {
            color: #166534;
            font-weight: 600;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .holiday-table {
                font-size: 0.875rem;
            }
            .holiday-table th,
            .holiday-table td {
                padding: 0.5rem;
            }
            .legend {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Navigation Bar -->
        <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <a class="mr-6 flex items-center space-x-2" href="index.html">
                        <span class="hidden font-bold sm:inline-block">PayCal Australia</span>
                    </a>
                    <nav class="flex items-center space-x-6 text-sm font-medium">
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Paycal.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="salary-tax-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Tax Calculator</a>
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>

                <!-- Mobile menu button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Mobile menu -->
                <div class="fixed left-0 top-0 z-50 grid h-[calc(100vh-4rem)] w-full translate-x-[-100%] grid-flow-row auto-rows-max overflow-auto p-6 pb-32 shadow-md animate-in slide-in-from-bottom-80 md:hidden hidden" id="mobile-menu">
                    <div class="relative z-20 grid gap-6 rounded-md bg-popover p-4 text-popover-foreground shadow-md">
                        <a class="flex items-center space-x-2" href="index.html">
                            <span class="font-bold">PayCal Australia</span>
                        </a>
                        <nav class="grid gap-2">
                            <a href="index.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Australian Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate salary, tax and superannuation</p>
                            </a>
                            <a href="contractor-pay-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Contractor Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate contractor rates and take-home pay</p>
                            </a>
                            <a href="mortgage-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Mortgage Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate home loan repayments</p>
                            </a>
                            <a href="novated-lease-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Novated Lease Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Compare EV vs ICE vehicle costs</p>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- PayCal Australia Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📅 New!</span>
                        <span class="text-sm sm:text-base">View <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="#holidays">Australian public holidays</a><span class="hidden sm:inline"> - Plan your long weekends!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#holidays">View Holidays</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Australian Public Holidays 2025
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Complete list of Australian public holidays for 2025. View national and state-specific public holidays, long weekends, and holiday dates for all Australian states and territories.
                    </p>
                </div>

                <!-- Next Holiday Countdown -->
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="upcoming-holidays">
                        <h3>🎉 Next Public Holiday</h3>
                        <div class="next-holiday">
                            <h4 id="next-holiday-name">Australia Day</h4>
                            <p id="next-holiday-date">Monday, 27 January 2025</p>
                            <p class="days-until" id="days-until">Coming up in <span id="days-count">45</span> days!</p>
                        </div>
                    </div>
                </div>

                <!-- State Filter -->
                <div class="max-w-4xl mx-auto mb-8" id="holidays">
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Filter by State/Territory</h2>
                        <div class="state-filter">
                            <select id="state-filter" onchange="filterHolidays()">
                                <option value="all">All States & Territories</option>
                                <option value="national">National Holidays Only</option>
                                <option value="NSW">New South Wales</option>
                                <option value="VIC">Victoria</option>
                                <option value="QLD">Queensland</option>
                                <option value="WA">Western Australia</option>
                                <option value="SA">South Australia</option>
                                <option value="TAS">Tasmania</option>
                                <option value="ACT">Australian Capital Territory</option>
                                <option value="NT">Northern Territory</option>
                            </select>
                        </div>

                        <!-- Legend -->
                        <div class="legend">
                            <div class="legend-item">
                                <div class="legend-color national"></div>
                                <span>National Holiday</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color state"></div>
                                <span>State/Territory Holiday</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color long-weekend"></div>
                                <span>Long Weekend</span>
                            </div>
                        </div>

                        <!-- Holidays Table -->
                        <table class="holiday-table" id="holidays-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Holiday</th>
                                    <th>Type</th>
                                    <th>States/Territories</th>
                                    <th>Long Weekend</th>
                                </tr>
                            </thead>
                            <tbody id="holidays-tbody">
                                <!-- Holidays will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-12 max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- About Public Holidays -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">About Australian Public Holidays</h3>
                            <p class="text-muted-foreground mb-4">
                                Australia has both national public holidays that apply across the country and state/territory-specific holidays. Public holidays provide additional paid leave for employees and are important for planning business operations.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    National holidays apply to all states and territories
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    State holidays vary by location
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Some holidays create long weekends
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Check your award or enterprise agreement for entitlements
                                </li>
                            </ul>
                        </div>

                        <!-- Holiday Planning Tips -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">Holiday Planning Tips</h3>
                            <p class="text-muted-foreground mb-4">
                                Make the most of public holidays with smart planning and preparation.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Book travel early for long weekends
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Plan annual leave around public holidays
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Check business hours for services
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Consider penalty rates for weekend work
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 PayCal.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Public holidays data for 2025
        const PUBLIC_HOLIDAYS = [
            { date: '2025-01-01', name: 'New Year\'s Day', type: 'national', states: ['ALL'], longWeekend: false },
            { date: '2025-01-27', name: 'Australia Day', type: 'national', states: ['ALL'], longWeekend: true },
            { date: '2025-03-10', name: 'Labour Day', type: 'state', states: ['VIC', 'TAS', 'WA'], longWeekend: true },
            { date: '2025-03-17', name: 'Canberra Day', type: 'state', states: ['ACT'], longWeekend: true },
            { date: '2025-04-18', name: 'Good Friday', type: 'national', states: ['ALL'], longWeekend: true },
            { date: '2025-04-19', name: 'Easter Saturday', type: 'state', states: ['NSW', 'VIC', 'QLD', 'SA', 'WA', 'TAS', 'ACT', 'NT'], longWeekend: true },
            { date: '2025-04-21', name: 'Easter Monday', type: 'national', states: ['ALL'], longWeekend: true },
            { date: '2025-04-25', name: 'ANZAC Day', type: 'national', states: ['ALL'], longWeekend: false },
            { date: '2025-05-05', name: 'Labour Day', type: 'state', states: ['QLD', 'NT'], longWeekend: true },
            { date: '2025-06-09', name: 'Queen\'s Birthday', type: 'state', states: ['NSW', 'VIC', 'QLD', 'SA', 'TAS', 'ACT', 'NT'], longWeekend: true },
            { date: '2025-08-04', name: 'Bank Holiday', type: 'state', states: ['NSW'], longWeekend: true },
            { date: '2025-10-06', name: 'Queen\'s Birthday', type: 'state', states: ['WA'], longWeekend: true },
            { date: '2025-10-13', name: 'Labour Day', type: 'state', states: ['NSW', 'ACT', 'SA'], longWeekend: true },
            { date: '2025-11-04', name: 'Melbourne Cup Day', type: 'state', states: ['VIC'], longWeekend: false },
            { date: '2025-12-25', name: 'Christmas Day', type: 'national', states: ['ALL'], longWeekend: false },
            { date: '2025-12-26', name: 'Boxing Day', type: 'national', states: ['ALL'], longWeekend: false }
        ];

        let currentFilter = 'all';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateNextHoliday();
            renderHolidays();
        });

        // Update next holiday countdown
        function updateNextHoliday() {
            const today = new Date();
            const upcomingHolidays = PUBLIC_HOLIDAYS
                .filter(holiday => new Date(holiday.date) > today)
                .sort((a, b) => new Date(a.date) - new Date(b.date));

            if (upcomingHolidays.length > 0) {
                const nextHoliday = upcomingHolidays[0];
                const holidayDate = new Date(nextHoliday.date);
                const daysUntil = Math.ceil((holidayDate - today) / (1000 * 60 * 60 * 24));

                document.getElementById('next-holiday-name').textContent = nextHoliday.name;
                document.getElementById('next-holiday-date').textContent = formatDate(nextHoliday.date);
                document.getElementById('days-count').textContent = daysUntil;
            }
        }

        // Filter holidays by state
        function filterHolidays() {
            const filter = document.getElementById('state-filter').value;
            currentFilter = filter;
            renderHolidays();
        }

        // Render holidays table
        function renderHolidays() {
            const tbody = document.getElementById('holidays-tbody');

            let filteredHolidays = PUBLIC_HOLIDAYS;
            if (currentFilter !== 'all') {
                if (currentFilter === 'national') {
                    filteredHolidays = PUBLIC_HOLIDAYS.filter(holiday => holiday.type === 'national');
                } else {
                    filteredHolidays = PUBLIC_HOLIDAYS.filter(holiday =>
                        holiday.states.includes('ALL') || holiday.states.includes(currentFilter)
                    );
                }
            }

            tbody.innerHTML = '';
            filteredHolidays.forEach(holiday => {
                const row = document.createElement('tr');
                const rowClass = holiday.type === 'national' ? 'national' :
                                holiday.longWeekend ? 'long-weekend' : 'state';
                row.className = rowClass;

                row.innerHTML = `
                    <td>${formatDate(holiday.date)}</td>
                    <td>${holiday.name}</td>
                    <td>${holiday.type === 'national' ? 'National' : 'State/Territory'}</td>
                    <td>${holiday.states.includes('ALL') ? 'All States & Territories' : holiday.states.join(', ')}</td>
                    <td>${holiday.longWeekend ? '✓' : ''}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-AU', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }

        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }
    </script>
    <script src="fix-navigation.js"></script>
</body>
</html>
