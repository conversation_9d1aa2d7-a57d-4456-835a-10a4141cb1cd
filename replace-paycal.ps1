# PowerShell script to replace PayCal with Calculator City in all HTML files

$htmlFiles = Get-ChildItem -Path "." -Filter "*.html"

Write-Host "Found $($htmlFiles.Count) HTML files to process..."

foreach ($file in $htmlFiles) {
    Write-Host "Processing: $($file.Name)"

    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content

    $content = $content -replace "PayCal Australia", "Calculator City"
    $content = $content -replace "PayCal", "Calculator City"
    $content = $content -replace "Paycal\.com\.au", "Calculator City"
    $content = $content -replace "paycal\.com\.au", "calculatorcity.com.au"
    $content = $content -replace "Paycal", "Calculator City"
    $content = $content -replace "paycal", "calculator city"

    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        Write-Host "Updated $($file.Name)"
    } else {
        Write-Host "No changes needed for $($file.Name)"
    }
}

Write-Host "Replacement complete!"
