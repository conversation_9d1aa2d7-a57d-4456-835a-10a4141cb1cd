﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian School Holidays 2025 | School Holiday Calendar | Calculator City</title>
    <meta name="description" content="Complete Australian school holidays calendar for 2025. View term dates and school holiday periods for all Australian states and territories. Plan your family holidays with our comprehensive school calendar.">
    <meta name="keywords" content="australian school holidays,school holidays 2025,school term dates,australia school calendar,state school holidays,term dates australia,school holiday calendar">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .holiday-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .holiday-table th,
        .holiday-table td {
            border: 1px solid hsl(var(--border));
            padding: 0.75rem;
            text-align: left;
        }

        .holiday-table th {
            background: hsl(var(--muted));
            font-weight: 600;
        }

        .holiday-table .term {
            background: #dbeafe;
        }

        .holiday-table .holiday {
            background: #fef3c7;
        }

        .holiday-table .summer {
            background: #fef2f2;
        }

        .state-filter {
            margin-bottom: 1rem;
        }

        .state-filter select {
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .legend {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .legend-color {
            width: 1rem;
            height: 1rem;
            border-radius: 0.25rem;
            border: 1px solid hsl(var(--border));
        }

        .legend-color.term {
            background: #dbeafe;
        }

        .legend-color.holiday {
            background: #fef3c7;
        }

        .legend-color.summer {
            background: #fef2f2;
        }

        .upcoming-holidays {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-bottom: 1rem;
        }

        .upcoming-holidays h3 {
            margin: 0 0 1rem 0;
            color: hsl(var(--foreground));
        }

        .next-holiday {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .next-holiday h4 {
            color: #92400e;
            margin: 0 0 0.5rem 0;
            font-weight: 600;
        }

        .days-until {
            color: #92400e;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .term-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .term-card {
            border: 2px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 1rem;
        }

        .term-card h4 {
            margin: 0 0 0.5rem 0;
            color: hsl(var(--foreground));
            font-weight: 600;
        }

        .term-card.current {
            border-color: #16a34a;
            background: #f0fdf4;
        }

        .term-card.current h4 {
            color: #166534;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .holiday-table {
                font-size: 0.875rem;
            }
            .holiday-table th,
            .holiday-table td {
                padding: 0.5rem;
            }
            .legend {
                flex-direction: column;
                gap: 0.5rem;
            }
            .term-overview {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Navigation Bar -->
        <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <a class="mr-6 flex items-center space-x-2" href="index.html">
                        <span class="hidden font-bold sm:inline-block">Calculator City</span>
                    </a>
                    <nav class="flex items-center space-x-6 text-sm font-medium">
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Calculator City.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="salary-tax-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Tax Calculator</a>
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>

                <!-- Mobile menu button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Mobile menu -->
                <div class="fixed left-0 top-0 z-50 grid h-[calc(100vh-4rem)] w-full translate-x-[-100%] grid-flow-row auto-rows-max overflow-auto p-6 pb-32 shadow-md animate-in slide-in-from-bottom-80 md:hidden hidden" id="mobile-menu">
                    <div class="relative z-20 grid gap-6 rounded-md bg-popover p-4 text-popover-foreground shadow-md">
                        <a class="flex items-center space-x-2" href="index.html">
                            <span class="font-bold">Calculator City</span>
                        </a>
                        <nav class="grid gap-2">
                            <a href="index.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Australian Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate salary, tax and superannuation</p>
                            </a>
                            <a href="contractor-pay-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Contractor Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate contractor rates and take-home pay</p>
                            </a>
                            <a href="mortgage-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Mortgage Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate home loan repayments</p>
                            </a>
                            <a href="novated-lease-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Novated Lease Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Compare EV vs ICE vehicle costs</p>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- Calculator City Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">🎒 New!</span>
                        <span class="text-sm sm:text-base">View <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="#holidays">school holidays</a><span class="hidden sm:inline"> - Plan your family time!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#holidays">View Calendar</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Australian School Holidays 2025
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Complete school holiday calendar for all Australian states and territories. Plan your family holidays and activities with our comprehensive school holiday dates.
                    </p>
                </div>

                <!-- State Filter -->
                <div class="max-w-4xl mx-auto mb-8" id="holidays">
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Filter by State/Territory</h2>
                        <div class="state-filter">
                            <select id="state-filter" onchange="filterHolidays()">
                                <option value="all">All States & Territories</option>
                                <option value="NSW">New South Wales</option>
                                <option value="VIC">Victoria</option>
                                <option value="QLD">Queensland</option>
                                <option value="WA">Western Australia</option>
                                <option value="SA">South Australia</option>
                                <option value="TAS">Tasmania</option>
                                <option value="ACT">Australian Capital Territory</option>
                                <option value="NT">Northern Territory</option>
                            </select>
                        </div>

                        <!-- Holidays Table -->
                        <table class="holiday-table" id="holidays-table">
                            <thead>
                                <tr>
                                    <th>Term/Holiday</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Duration</th>
                                    <th>States/Territories</th>
                                </tr>
                            </thead>
                            <tbody id="holidays-tbody">
                                <!-- Holidays will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-12 max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- About School Holidays -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">About Australian School Holidays</h3>
                            <p class="text-muted-foreground mb-4">
                                Australian school holidays vary by state and territory. Each state sets its own school calendar, typically following a four-term structure with breaks between terms and a longer summer holiday.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Four terms per year in most states
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Summer holidays are the longest break
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Dates vary between states and territories
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Private schools may have different dates
                                </li>
                            </ul>
                        </div>

                        <!-- Holiday Planning Tips -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">Holiday Planning Tips</h3>
                            <p class="text-muted-foreground mb-4">
                                Make the most of school holidays with smart planning and preparation.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Book family holidays early for better prices
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Plan activities and camps in advance
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Consider childcare arrangements
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Check local event calendars
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 Calculator City.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // School holidays data for 2025
        const SCHOOL_HOLIDAYS = [
            { term: 'Term 1', start: '2025-01-28', end: '2025-04-11', states: ['NSW', 'ACT'] },
            { term: 'Term 1', start: '2025-01-29', end: '2025-04-04', states: ['VIC'] },
            { term: 'Term 1', start: '2025-01-27', end: '2025-04-04', states: ['QLD'] },
            { term: 'Term 1', start: '2025-02-03', end: '2025-04-11', states: ['WA'] },
            { term: 'Term 1', start: '2025-01-27', end: '2025-04-11', states: ['SA'] },
            { term: 'Term 1', start: '2025-02-05', end: '2025-04-11', states: ['TAS'] },
            { term: 'Term 1', start: '2025-01-29', end: '2025-04-04', states: ['NT'] },

            { term: 'Autumn Holidays', start: '2025-04-12', end: '2025-04-27', states: ['NSW', 'ACT'] },
            { term: 'Autumn Holidays', start: '2025-04-05', end: '2025-04-21', states: ['VIC'] },
            { term: 'Autumn Holidays', start: '2025-04-05', end: '2025-04-21', states: ['QLD'] },
            { term: 'Autumn Holidays', start: '2025-04-12', end: '2025-04-27', states: ['WA'] },
            { term: 'Autumn Holidays', start: '2025-04-12', end: '2025-04-27', states: ['SA'] },
            { term: 'Autumn Holidays', start: '2025-04-12', end: '2025-04-27', states: ['TAS'] },
            { term: 'Autumn Holidays', start: '2025-04-05', end: '2025-04-21', states: ['NT'] },
        ];

        let currentFilter = 'all';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderHolidays();
        });

        // Filter holidays by state
        function filterHolidays() {
            const filter = document.getElementById('state-filter').value;
            currentFilter = filter;
            renderHolidays();
        }

        // Render holidays table
        function renderHolidays() {
            const tbody = document.getElementById('holidays-tbody');

            let filteredHolidays = SCHOOL_HOLIDAYS;
            if (currentFilter !== 'all') {
                filteredHolidays = SCHOOL_HOLIDAYS.filter(holiday =>
                    holiday.states.includes(currentFilter)
                );
            }

            tbody.innerHTML = '';
            filteredHolidays.forEach(holiday => {
                const row = document.createElement('tr');
                const duration = calculateDuration(holiday.start, holiday.end);

                row.innerHTML = `
                    <td>${holiday.term}</td>
                    <td>${formatDate(holiday.start)}</td>
                    <td>${formatDate(holiday.end)}</td>
                    <td>${duration} days</td>
                    <td>${holiday.states.join(', ')}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // Calculate duration between dates
        function calculateDuration(startDate, endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            return diffDays;
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-AU', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }

        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }
    </script>
    <script src="fix-navigation.js"></script>
</body>
</html>
