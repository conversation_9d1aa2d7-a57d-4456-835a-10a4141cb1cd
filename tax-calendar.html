﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian Tax Calendar 2024-2025 | Important Tax Dates | Calculator City</title>
    <meta name="description" content="Complete Australian tax calendar with all important dates and deadlines for 2024-2025. Never miss a tax deadline with our comprehensive tax calendar.">
    <meta name="keywords" content="australian tax calendar,tax deadlines,tax dates 2024,tax dates 2025,ato deadlines,tax return deadline,bas deadline,payroll tax">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .tax-event {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.2s;
        }

        .tax-event:hover {
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .tax-event.urgent {
            border-left: 4px solid #dc2626;
            background: #fef2f2;
        }

        .tax-event.upcoming {
            border-left: 4px solid #f59e0b;
            background: #fffbeb;
        }

        .tax-event.normal {
            border-left: 4px solid #3b82f6;
            background: #eff6ff;
        }

        .tax-event.completed {
            border-left: 4px solid #16a34a;
            background: #f0fdf4;
            opacity: 0.7;
        }

        .month-header {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 1rem;
            border-radius: var(--radius);
            margin: 2rem 0 1rem 0;
            text-align: center;
            font-weight: 600;
            font-size: 1.25rem;
        }

        .filter-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .filter-tab {
            padding: 0.5rem 1rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.875rem;
        }

        .filter-tab:hover {
            background: hsl(var(--muted));
        }

        .filter-tab.active {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
        }

        .countdown {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .countdown h3 {
            color: #92400e;
            margin: 0 0 0.5rem 0;
            font-weight: 600;
        }

        .date-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .urgent-badge {
            background: #fecaca;
            color: #991b1b;
        }

        .upcoming-badge {
            background: #fef3c7;
            color: #92400e;
        }

        .normal-badge {
            background: #dbeafe;
            color: #1e40af;
        }

        .completed-badge {
            background: #dcfce7;
            color: #166534;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .tax-event {
                padding: 0.75rem;
            }
            .filter-tabs {
                justify-content: center;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Navigation Bar -->
        <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <a class="mr-6 flex items-center space-x-2" href="index.html">
                        <span class="hidden font-bold sm:inline-block">Calculator City</span>
                    </a>
                    <nav class="flex items-center space-x-6 text-sm font-medium">
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Calculator City.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="salary-tax-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Tax Calculator</a>
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[250px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden bg-background border rounded-md shadow-lg min-w-[200px] z-50" data-dropdown>
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>

                <!-- Mobile menu button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Mobile menu -->
                <div class="fixed left-0 top-0 z-50 grid h-[calc(100vh-4rem)] w-full translate-x-[-100%] grid-flow-row auto-rows-max overflow-auto p-6 pb-32 shadow-md animate-in slide-in-from-bottom-80 md:hidden hidden" id="mobile-menu">
                    <div class="relative z-20 grid gap-6 rounded-md bg-popover p-4 text-popover-foreground shadow-md">
                        <a class="flex items-center space-x-2" href="index.html">
                            <span class="font-bold">Calculator City</span>
                        </a>
                        <nav class="grid gap-2">
                            <a href="index.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Australian Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate salary, tax and superannuation</p>
                            </a>
                            <a href="contractor-pay-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Contractor Pay Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate contractor rates and take-home pay</p>
                            </a>
                            <a href="mortgage-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Mortgage Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Calculate home loan repayments</p>
                            </a>
                            <a href="novated-lease-calculator.html" class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                                <div class="text-sm font-medium leading-none">Novated Lease Calculator</div>
                                <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">Compare EV vs ICE vehicle costs</p>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- Calculator City Header -->
        <div class="bg-orange-100/90 border-b border-orange-200 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📅 Track!</span>
                        <span class="text-sm sm:text-base">Never miss <a class="font-semibold text-orange-600 hover:underline" href="#calendar">tax deadlines</a><span class="hidden sm:inline"> - Stay compliant!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 hover:bg-orange-200/70" href="#calendar">View Calendar</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Australian Tax Calendar 2024-2025
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Complete tax calendar with all important dates and deadlines. Never miss a tax deadline with our comprehensive Australian tax calendar.
                    </p>
                </div>

                <!-- Next Deadline Countdown -->
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="countdown">
                        <h3>⏰ Next Important Deadline</h3>
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold text-lg" id="next-deadline">Individual Tax Returns</div>
                                <div class="text-sm" id="next-deadline-date">Due 31 October 2024</div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold" id="days-until">45</div>
                                <div class="text-sm">days remaining</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Tabs -->
                <div class="max-w-4xl mx-auto mb-8" id="calendar">
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Filter by Category</h2>
                        <div class="filter-tabs">
                            <button class="filter-tab active" onclick="filterEvents('all')">All Events</button>
                            <button class="filter-tab" onclick="filterEvents('individual')">Individual</button>
                            <button class="filter-tab" onclick="filterEvents('business')">Business</button>
                            <button class="filter-tab" onclick="filterEvents('bas')">BAS</button>
                            <button class="filter-tab" onclick="filterEvents('payroll')">Payroll</button>
                            <button class="filter-tab" onclick="filterEvents('super')">Superannuation</button>
                            <button class="filter-tab" onclick="filterEvents('urgent')">Urgent</button>
                        </div>
                    </div>
                </div>

                <!-- Tax Events Timeline -->
                <div class="max-w-4xl mx-auto">
                    <div id="tax-events-container">
                        <!-- Tax events will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-12 max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- About Tax Deadlines -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">About Australian Tax Deadlines</h3>
                            <p class="text-muted-foreground mb-4">
                                The Australian Taxation Office (ATO) sets specific deadlines for various tax obligations. Missing these deadlines can result in penalties and interest charges.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-3 h-3 bg-red-600 rounded-full"></span>
                                    Urgent: Due within 7 days
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-3 h-3 bg-orange-600 rounded-full"></span>
                                    Upcoming: Due within 30 days
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-3 h-3 bg-blue-600 rounded-full"></span>
                                    Normal: Future deadlines
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-3 h-3 bg-green-600 rounded-full"></span>
                                    Completed: Past deadlines
                                </li>
                            </ul>
                        </div>

                        <!-- Tax Planning Tips -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">Tax Planning Tips</h3>
                            <p class="text-muted-foreground mb-4">
                                Stay organized and compliant with these essential tax planning strategies.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Set calendar reminders for all deadlines
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Keep accurate records throughout the year
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Consider using a tax agent for complex returns
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Lodge early to avoid last-minute rush
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 Calculator City.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Tax events data for 2024-2025
        const TAX_EVENTS = [
            // 2024 Events
            { date: '2024-07-28', title: 'BAS Quarter 4 (2023-24)', category: 'bas', description: 'Business Activity Statement due for April-June 2024' },
            { date: '2024-08-28', title: 'Monthly BAS July 2024', category: 'bas', description: 'Monthly BAS for July 2024' },
            { date: '2024-09-28', title: 'Monthly BAS August 2024', category: 'bas', description: 'Monthly BAS for August 2024' },
            { date: '2024-10-21', title: 'PAYG Instalment Q1', category: 'business', description: 'PAYG instalment for July-September 2024' },
            { date: '2024-10-28', title: 'Monthly BAS September 2024', category: 'bas', description: 'Monthly BAS for September 2024' },
            { date: '2024-10-31', title: 'Individual Tax Returns', category: 'individual', description: 'Individual tax returns for 2023-24 financial year' },
            { date: '2024-10-28', title: 'BAS Quarter 1 (2024-25)', category: 'bas', description: 'Business Activity Statement due for July-September 2024' },
            { date: '2024-11-28', title: 'Monthly BAS October 2024', category: 'bas', description: 'Monthly BAS for October 2024' },
            { date: '2024-12-28', title: 'Monthly BAS November 2024', category: 'bas', description: 'Monthly BAS for November 2024' },

            // 2025 Events
            { date: '2025-01-21', title: 'PAYG Instalment Q2', category: 'business', description: 'PAYG instalment for October-December 2024' },
            { date: '2025-01-28', title: 'Monthly BAS December 2024', category: 'bas', description: 'Monthly BAS for December 2024' },
            { date: '2025-01-28', title: 'BAS Quarter 2 (2024-25)', category: 'bas', description: 'Business Activity Statement due for October-December 2024' },
            { date: '2025-02-28', title: 'Monthly BAS January 2025', category: 'bas', description: 'Monthly BAS for January 2025' },
            { date: '2025-03-28', title: 'Monthly BAS February 2025', category: 'bas', description: 'Monthly BAS for February 2025' },
            { date: '2025-04-21', title: 'PAYG Instalment Q3', category: 'business', description: 'PAYG instalment for January-March 2025' },
            { date: '2025-04-28', title: 'Monthly BAS March 2025', category: 'bas', description: 'Monthly BAS for March 2025' },
            { date: '2025-04-28', title: 'BAS Quarter 3 (2024-25)', category: 'bas', description: 'Business Activity Statement due for January-March 2025' },
            { date: '2025-05-15', title: 'Company Tax Returns', category: 'business', description: 'Company tax returns for 2024-25 financial year' },
            { date: '2025-05-28', title: 'Monthly BAS April 2025', category: 'bas', description: 'Monthly BAS for April 2025' },
            { date: '2025-06-28', title: 'Monthly BAS May 2025', category: 'bas', description: 'Monthly BAS for May 2025' },
            { date: '2025-06-30', title: 'Financial Year End', category: 'business', description: 'End of 2024-25 financial year' }
        ];

        let currentFilter = 'all';

        // Initialize calendar
        document.addEventListener('DOMContentLoaded', function() {
            updateNextDeadline();
            renderTaxEvents();
        });

        // Update next deadline countdown
        function updateNextDeadline() {
            const today = new Date();
            const upcomingEvents = TAX_EVENTS
                .filter(event => new Date(event.date) > today)
                .sort((a, b) => new Date(a.date) - new Date(b.date));

            if (upcomingEvents.length > 0) {
                const nextEvent = upcomingEvents[0];
                const eventDate = new Date(nextEvent.date);
                const daysUntil = Math.ceil((eventDate - today) / (1000 * 60 * 60 * 24));

                document.getElementById('next-deadline').textContent = nextEvent.title;
                document.getElementById('next-deadline-date').textContent = `Due ${formatDate(nextEvent.date)}`;
                document.getElementById('days-until').textContent = daysUntil;
            }
        }

        // Render tax events
        function renderTaxEvents() {
            const container = document.getElementById('tax-events-container');
            const today = new Date();

            // Filter events based on current filter
            let filteredEvents = TAX_EVENTS;
            if (currentFilter !== 'all') {
                if (currentFilter === 'urgent') {
                    filteredEvents = TAX_EVENTS.filter(event => {
                        const eventDate = new Date(event.date);
                        const daysUntil = Math.ceil((eventDate - today) / (1000 * 60 * 60 * 24));
                        return daysUntil <= 7 && daysUntil >= 0;
                    });
                } else {
                    filteredEvents = TAX_EVENTS.filter(event => event.category === currentFilter);
                }
            }

            // Group events by month
            const eventsByMonth = {};
            filteredEvents.forEach(event => {
                const eventDate = new Date(event.date);
                const monthKey = `${eventDate.getFullYear()}-${eventDate.getMonth()}`;
                if (!eventsByMonth[monthKey]) {
                    eventsByMonth[monthKey] = [];
                }
                eventsByMonth[monthKey].push(event);
            });

            // Render events
            container.innerHTML = '';
            Object.keys(eventsByMonth).sort().forEach(monthKey => {
                const [year, month] = monthKey.split('-');
                const monthDate = new Date(year, month);
                const monthName = monthDate.toLocaleDateString('en-AU', { month: 'long', year: 'numeric' });

                // Add month header
                const monthHeader = document.createElement('div');
                monthHeader.className = 'month-header';
                monthHeader.textContent = monthName;
                container.appendChild(monthHeader);

                // Add events for this month
                eventsByMonth[monthKey]
                    .sort((a, b) => new Date(a.date) - new Date(b.date))
                    .forEach(event => {
                        const eventElement = createEventElement(event, today);
                        container.appendChild(eventElement);
                    });
            });
        }

        // Create event element
        function createEventElement(event, today) {
            const eventDate = new Date(event.date);
            const daysUntil = Math.ceil((eventDate - today) / (1000 * 60 * 60 * 24));

            let eventClass = 'normal';
            let badgeClass = 'normal-badge';

            if (daysUntil < 0) {
                eventClass = 'completed';
                badgeClass = 'completed-badge';
            } else if (daysUntil <= 7) {
                eventClass = 'urgent';
                badgeClass = 'urgent-badge';
            } else if (daysUntil <= 30) {
                eventClass = 'upcoming';
                badgeClass = 'upcoming-badge';
            }

            const eventDiv = document.createElement('div');
            eventDiv.className = `tax-event ${eventClass}`;
            eventDiv.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="date-badge ${badgeClass}">${formatDate(event.date)}</div>
                        <h4 class="font-semibold text-lg mb-1">${event.title}</h4>
                        <p class="text-sm text-muted-foreground">${event.description}</p>
                        <div class="mt-2">
                            <span class="inline-block px-2 py-1 text-xs rounded-full bg-muted text-muted-foreground">
                                ${getCategoryName(event.category)}
                            </span>
                        </div>
                    </div>
                    <div class="text-right ml-4">
                        <div class="text-sm font-medium">
                            ${daysUntil < 0 ? 'Completed' :
                              daysUntil === 0 ? 'Due Today' :
                              daysUntil === 1 ? 'Due Tomorrow' :
                              `${daysUntil} days`}
                        </div>
                    </div>
                </div>
            `;

            return eventDiv;
        }

        // Filter events
        function filterEvents(category) {
            currentFilter = category;

            // Update active tab
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            renderTaxEvents();
        }

        // Get category display name
        function getCategoryName(category) {
            const categoryNames = {
                individual: 'Individual',
                business: 'Business',
                bas: 'BAS',
                payroll: 'Payroll',
                super: 'Superannuation'
            };
            return categoryNames[category] || category;
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-AU', {
                day: 'numeric',
                month: 'short',
                year: 'numeric'
            });
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }

        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }
    </script>
    <script src="fix-navigation.js"></script>
</body>
</html>
