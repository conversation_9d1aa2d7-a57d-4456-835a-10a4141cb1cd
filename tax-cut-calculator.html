﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Cut Calculator | Australian Tax Savings Calculator | Calculator City</title>
    <meta name="description" content="Calculate your Australian tax savings from the latest tax cuts and reforms. See how much extra take-home pay you'll receive with updated tax brackets and thresholds.">
    <meta name="keywords" content="tax cut calculator,australian tax cuts,tax savings calculator,tax reform calculator,stage 3 tax cuts,tax bracket calculator,income tax savings,australian tax changes">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .results-container {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .result-value.positive {
            color: #16a34a;
        }

        .result-value.negative {
            color: #dc2626;
        }

        .savings-highlight {
            background: #dcfce7;
            border: 2px solid #16a34a;
            border-radius: var(--radius);
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }

        .savings-highlight h3 {
            color: #166534;
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .savings-highlight .amount {
            color: #166534;
            font-size: 2rem;
            font-weight: 800;
            margin: 0.5rem 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid hsl(var(--border));
            padding: 0.75rem;
            text-align: left;
        }

        .comparison-table th {
            background: hsl(var(--muted));
            font-weight: 600;
        }

        .comparison-table .old-tax {
            color: #dc2626;
        }

        .comparison-table .new-tax {
            color: #16a34a;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .savings-highlight .amount {
                font-size: 1.5rem;
            }
            .comparison-table {
                font-size: 0.875rem;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Calculator City Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">💸 New!</span>
                        <span class="text-sm sm:text-base">Calculate your <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="#calculator">tax savings</a><span class="hidden sm:inline"> - See how much you'll save!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#calculator">Calculate Savings</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Tax Cut Calculator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Calculate your Australian tax savings from the latest tax cuts and reforms. See how much extra take-home pay you'll receive with updated tax brackets.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto" id="calculator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Tax Cut Calculator</h2>

                        <form class="grid gap-4" id="tax-form">
                            <!-- Income Input -->
                            <div class="input-group">
                                <label for="income">Annual Income</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input
                                        type="number"
                                        id="income"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                        min="0"
                                        placeholder="Enter your annual income"
                                        value="80000"
                                        oninput="calculateTaxCut()"
                                    />
                                </div>
                            </div>

                            <!-- Tax Year Comparison -->
                            <div class="input-group">
                                <label for="comparisonType">Tax Year Comparison</label>
                                <select
                                    id="comparisonType"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateTaxCut()"
                                >
                                    <option value="2023-24-vs-2024-25" selected>2023-24 vs 2024-25</option>
                                    <option value="2022-23-vs-2024-25">2022-23 vs 2024-25</option>
                                    <option value="2021-22-vs-2024-25">2021-22 vs 2024-25</option>
                                </select>
                            </div>

                            <!-- Quick Income Buttons -->
                            <div class="mt-4">
                                <label class="text-sm font-medium mb-3 block">Quick Income Amounts</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <button type="button" class="btn-secondary" onclick="setIncome(50000)">$50,000</button>
                                    <button type="button" class="btn-secondary" onclick="setIncome(80000)">$80,000</button>
                                    <button type="button" class="btn-secondary" onclick="setIncome(100000)">$100,000</button>
                                    <button type="button" class="btn-secondary" onclick="setIncome(150000)">$150,000</button>
                                    <button type="button" class="btn-secondary" onclick="setIncome(200000)">$200,000</button>
                                    <button type="button" class="btn-secondary" onclick="setIncome(300000)">$300,000</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Results Section -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Your Tax Savings</h2>

                        <!-- Savings Highlight -->
                        <div class="savings-highlight">
                            <h3>Annual Tax Savings</h3>
                            <div class="amount" id="annual-savings">$1,200</div>
                            <p class="text-sm">Extra money in your pocket each year!</p>
                        </div>

                        <div class="results-container" id="tax-results">
                            <div class="result-item">
                                <span class="result-label">Old Tax (2023-24)</span>
                                <span class="result-value negative" id="old-tax">$18,067</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">New Tax (2024-25)</span>
                                <span class="result-value negative" id="new-tax">$16,867</span>
                            </div>
                            <div class="result-item border-t-2 border-green-500 pt-4">
                                <span class="result-label font-semibold">Tax Savings</span>
                                <span class="result-value positive text-xl font-bold" id="tax-savings">$1,200</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Weekly Savings</span>
                                <span class="result-value positive" id="weekly-savings">$23</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Monthly Savings</span>
                                <span class="result-value positive" id="monthly-savings">$100</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tax Brackets Comparison -->
                <div class="mt-12 max-w-6xl mx-auto">
                    <div class="calculator-container p-6">
                        <h3 class="text-2xl font-semibold mb-6 text-center">Tax Brackets Comparison</h3>
                        <div class="overflow-x-auto">
                            <table class="comparison-table">
                                <thead>
                                    <tr>
                                        <th>Taxable Income</th>
                                        <th>2023-24 Tax Rate</th>
                                        <th>2024-25 Tax Rate</th>
                                        <th>Change</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>$0 – $18,200</td>
                                        <td>0%</td>
                                        <td>0%</td>
                                        <td>No change</td>
                                    </tr>
                                    <tr>
                                        <td>$18,201 – $45,000</td>
                                        <td class="old-tax">19%</td>
                                        <td class="new-tax">19%</td>
                                        <td>No change</td>
                                    </tr>
                                    <tr>
                                        <td>$45,001 – $120,000</td>
                                        <td class="old-tax">32.5%</td>
                                        <td class="new-tax">32.5%</td>
                                        <td>No change</td>
                                    </tr>
                                    <tr>
                                        <td>$120,001 – $180,000</td>
                                        <td class="old-tax">37%</td>
                                        <td class="new-tax">37%</td>
                                        <td>No change</td>
                                    </tr>
                                    <tr>
                                        <td>$180,001+</td>
                                        <td class="old-tax">45%</td>
                                        <td class="new-tax">45%</td>
                                        <td>No change</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p class="text-sm text-muted-foreground mt-4 text-center">
                            * Tax cuts may include changes to tax offsets and thresholds
                        </p>
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-12 max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- About Tax Cuts -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">About Australian Tax Cuts</h3>
                            <p class="text-muted-foreground mb-4">
                                The Australian government regularly reviews and adjusts tax brackets and rates to provide relief to taxpayers.
                                These changes can result in significant savings for individuals and families.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Lower tax rates for middle income earners
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Increased tax-free thresholds
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Enhanced tax offsets
                                </li>
                            </ul>
                        </div>

                        <!-- How to Use Savings -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">How to Use Your Tax Savings</h3>
                            <p class="text-muted-foreground mb-4">
                                Your tax savings can make a real difference to your financial situation. Consider these options:
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Pay down debt faster
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Boost your emergency fund
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Increase superannuation contributions
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Invest for the future
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 Calculator City.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Tax brackets for different years
        const TAX_BRACKETS = {
            '2021-22': [
                { min: 0, max: 18200, rate: 0 },
                { min: 18201, max: 45000, rate: 0.19 },
                { min: 45001, max: 120000, rate: 0.325 },
                { min: 120001, max: 180000, rate: 0.37 },
                { min: 180001, max: Infinity, rate: 0.45 }
            ],
            '2022-23': [
                { min: 0, max: 18200, rate: 0 },
                { min: 18201, max: 45000, rate: 0.19 },
                { min: 45001, max: 120000, rate: 0.325 },
                { min: 120001, max: 180000, rate: 0.37 },
                { min: 180001, max: Infinity, rate: 0.45 }
            ],
            '2023-24': [
                { min: 0, max: 18200, rate: 0 },
                { min: 18201, max: 45000, rate: 0.19 },
                { min: 45001, max: 120000, rate: 0.325 },
                { min: 120001, max: 180000, rate: 0.37 },
                { min: 180001, max: Infinity, rate: 0.45 }
            ],
            '2024-25': [
                { min: 0, max: 18200, rate: 0 },
                { min: 18201, max: 45000, rate: 0.19 },
                { min: 45001, max: 120000, rate: 0.325 },
                { min: 120001, max: 180000, rate: 0.37 },
                { min: 180001, max: Infinity, rate: 0.45 }
            ]
        };

        // Low and Middle Income Tax Offset (LMITO) - simplified
        const TAX_OFFSETS = {
            '2021-22': { threshold: 37000, maxOffset: 1080 },
            '2022-23': { threshold: 37000, maxOffset: 1500 },
            '2023-24': { threshold: 37000, maxOffset: 1500 },
            '2024-25': { threshold: 37000, maxOffset: 700 } // Reduced LMITO
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            calculateTaxCut();
        });

        // Set income amount
        function setIncome(amount) {
            document.getElementById('income').value = amount;
            calculateTaxCut();
        }

        // Calculate tax for a given year
        function calculateTax(income, year) {
            const brackets = TAX_BRACKETS[year];
            let tax = 0;

            for (const bracket of brackets) {
                if (income > bracket.min) {
                    const taxableInBracket = Math.min(income - bracket.min, bracket.max - bracket.min);
                    tax += taxableInBracket * bracket.rate;
                }
            }

            // Apply tax offset (simplified)
            const offset = TAX_OFFSETS[year];
            if (income <= offset.threshold + 50000) { // Simplified offset calculation
                tax = Math.max(0, tax - offset.maxOffset);
            }

            return tax;
        }

        // Calculate tax cut savings
        function calculateTaxCut() {
            const income = parseFloat(document.getElementById('income').value || 80000);
            const comparisonType = document.getElementById('comparisonType').value;

            const [oldYear, newYear] = comparisonType.split('-vs-');

            const oldTax = calculateTax(income, oldYear);
            const newTax = calculateTax(income, newYear);
            const savings = oldTax - newTax;

            // Update display
            document.getElementById('old-tax').textContent = formatCurrency(oldTax);
            document.getElementById('new-tax').textContent = formatCurrency(newTax);
            document.getElementById('tax-savings').textContent = formatCurrency(savings);
            document.getElementById('annual-savings').textContent = formatCurrency(savings);
            document.getElementById('weekly-savings').textContent = formatCurrency(savings / 52);
            document.getElementById('monthly-savings').textContent = formatCurrency(savings / 12);
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>
</body>
</html>
