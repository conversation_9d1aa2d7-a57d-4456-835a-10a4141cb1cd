﻿<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle Registration Calculator | Car Registration Costs | Calculator City</title>
    <meta name="description" content="Calculate Australian vehicle registration costs for all states and territories. Compare rego fees, CTP insurance, and stamp duty for cars, motorcycles, and trucks.">
    <meta name="keywords" content="vehicle registration calculator,car registration cost,rego calculator,ctp insurance,stamp duty,vehicle registration fees,australian rego">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .state-info {
            background: hsl(var(--muted));
            padding: 1rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Calculator City Header -->
        <div class="bg-orange-100/90 border-b border-orange-200 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">🚗 Calculate!</span>
                        <span class="text-sm sm:text-base">Check <a class="font-semibold text-orange-600 hover:underline" href="#calculator">vehicle registration costs</a><span class="hidden sm:inline"> - All Australian states!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 hover:bg-orange-200/70" href="#calculator">Calculate Now</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Vehicle Registration Calculator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Calculate vehicle registration costs for all Australian states and territories. Compare rego fees, CTP insurance, and stamp duty for your vehicle.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto" id="calculator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Vehicle Details</h2>
                        
                        <form class="grid gap-4" id="vehicle-form">
                            <!-- State/Territory -->
                            <div class="input-group">
                                <label for="state">State/Territory</label>
                                <select 
                                    id="state"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateRegistration()"
                                >
                                    <option value="NSW">New South Wales (NSW)</option>
                                    <option value="VIC">Victoria (VIC)</option>
                                    <option value="QLD">Queensland (QLD)</option>
                                    <option value="WA">Western Australia (WA)</option>
                                    <option value="SA">South Australia (SA)</option>
                                    <option value="TAS">Tasmania (TAS)</option>
                                    <option value="ACT">Australian Capital Territory (ACT)</option>
                                    <option value="NT">Northern Territory (NT)</option>
                                </select>
                            </div>

                            <!-- Vehicle Type -->
                            <div class="input-group">
                                <label for="vehicleType">Vehicle Type</label>
                                <select 
                                    id="vehicleType"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateRegistration()"
                                >
                                    <option value="car">Car/Sedan</option>
                                    <option value="suv">SUV/4WD</option>
                                    <option value="ute">Ute/Truck</option>
                                    <option value="motorcycle">Motorcycle</option>
                                    <option value="trailer">Trailer</option>
                                </select>
                            </div>

                            <!-- Vehicle Value -->
                            <div class="input-group">
                                <label for="vehicleValue">Vehicle Value</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input 
                                        type="number" 
                                        id="vehicleValue"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                        min="0"
                                        placeholder="Enter vehicle value"
                                        value="25000"
                                        oninput="calculateRegistration()"
                                    />
                                </div>
                            </div>

                            <!-- Engine Size -->
                            <div class="input-group">
                                <label for="engineSize">Engine Size (Litres)</label>
                                <input 
                                    type="number"
                                    id="engineSize"
                                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="e.g., 2.0"
                                    value="2.0"
                                    step="0.1"
                                    min="0"
                                    oninput="calculateRegistration()"
                                />
                            </div>

                            <!-- Registration Period -->
                            <div class="input-group">
                                <label for="regPeriod">Registration Period</label>
                                <select 
                                    id="regPeriod"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateRegistration()"
                                >
                                    <option value="6">6 Months</option>
                                    <option value="12" selected>12 Months</option>
                                </select>
                            </div>

                            <!-- Vehicle Age -->
                            <div class="input-group">
                                <label for="vehicleAge">Vehicle Age (Years)</label>
                                <input 
                                    type="number"
                                    id="vehicleAge"
                                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="e.g., 5"
                                    value="5"
                                    min="0"
                                    oninput="calculateRegistration()"
                                />
                            </div>
                        </form>
                    </div>

                    <!-- Results Section -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Registration Costs</h2>
                        
                        <div class="results-container" id="registration-results">
                            <div class="result-item">
                                <span class="result-label">Registration Fee</span>
                                <span class="result-value" id="reg-fee">$312</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">CTP Insurance</span>
                                <span class="result-value" id="ctp-insurance">$456</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Stamp Duty</span>
                                <span class="result-value" id="stamp-duty">$750</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Admin Fees</span>
                                <span class="result-value" id="admin-fees">$45</span>
                            </div>
                            <div class="result-item border-t-2 border-primary pt-4">
                                <span class="result-label font-semibold">Total Cost</span>
                                <span class="result-value text-xl font-bold" id="total-cost">$1,563</span>
                            </div>
                        </div>

                        <!-- State Information -->
                        <div class="state-info">
                            <h4 class="font-semibold mb-2" id="state-info-title">NSW Registration Info</h4>
                            <div class="text-sm text-muted-foreground" id="state-info-content">
                                <p>• Registration valid for 12 months</p>
                                <p>• CTP insurance included</p>
                                <p>• Pink slip required annually</p>
                                <p>• Online renewal available</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-12 max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- About Vehicle Registration -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">About Vehicle Registration</h3>
                            <p class="text-muted-foreground mb-4">
                                Vehicle registration is mandatory in Australia and includes registration fees, CTP insurance, and various state-specific charges.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Registration fees vary by state
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    CTP insurance is compulsory
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Stamp duty applies to new registrations
                                </li>
                            </ul>
                        </div>

                        <!-- Registration Tips -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">Registration Tips</h3>
                            <p class="text-muted-foreground mb-4">
                                Save money and time with these vehicle registration tips for Australian drivers.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Renew online for convenience
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Compare CTP insurance providers
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Keep registration current to avoid fines
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Check for concessions and discounts
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 Calculator City.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Registration costs by state (simplified estimates)
        const REGISTRATION_COSTS = {
            NSW: {
                name: "New South Wales",
                regFee: { car: 312, suv: 312, ute: 312, motorcycle: 78, trailer: 156 },
                ctpBase: { car: 456, suv: 456, ute: 456, motorcycle: 234, trailer: 0 },
                adminFee: 45,
                info: [
                    "• Registration valid for 12 months",
                    "• CTP insurance included",
                    "• Pink slip required annually",
                    "• Online renewal available"
                ]
            },
            VIC: {
                name: "Victoria",
                regFee: { car: 284, suv: 284, ute: 284, motorcycle: 71, trailer: 142 },
                ctpBase: { car: 523, suv: 523, ute: 523, motorcycle: 261, trailer: 0 },
                adminFee: 38,
                info: [
                    "• Registration valid for 12 months",
                    "• TAC insurance included",
                    "• Roadworthy certificate required",
                    "• VicRoads online services"
                ]
            }
        };

        // Initialize calculator
        document.addEventListener('DOMContentLoaded', function() {
            calculateRegistration();
        });

        // Calculate registration costs
        function calculateRegistration() {
            const state = document.getElementById('state').value;
            const vehicleType = document.getElementById('vehicleType').value;
            const vehicleValue = parseFloat(document.getElementById('vehicleValue').value) || 25000;
            const regPeriod = parseInt(document.getElementById('regPeriod').value) || 12;
            const vehicleAge = parseInt(document.getElementById('vehicleAge').value) || 5;

            const stateCosts = REGISTRATION_COSTS[state] || REGISTRATION_COSTS.NSW;

            // Calculate registration fee
            let regFee = stateCosts.regFee[vehicleType] || stateCosts.regFee.car;
            if (regPeriod === 6) regFee = regFee * 0.6;

            // Calculate CTP insurance
            let ctpInsurance = stateCosts.ctpBase[vehicleType] || stateCosts.ctpBase.car;
            if (regPeriod === 6) ctpInsurance = ctpInsurance * 0.6;

            // Calculate stamp duty
            let stampDuty = 0;
            if (vehicleAge <= 1 || vehicleValue > 45000) {
                stampDuty = Math.min(vehicleValue * 0.03, 1500);
            }

            const adminFees = stateCosts.adminFee;
            const totalCost = regFee + ctpInsurance + stampDuty + adminFees;

            // Update display
            document.getElementById('reg-fee').textContent = formatCurrency(regFee);
            document.getElementById('ctp-insurance').textContent = formatCurrency(ctpInsurance);
            document.getElementById('stamp-duty').textContent = formatCurrency(stampDuty);
            document.getElementById('admin-fees').textContent = formatCurrency(adminFees);
            document.getElementById('total-cost').textContent = formatCurrency(totalCost);

            // Update state information
            document.getElementById('state-info-title').textContent = `${stateCosts.name} Registration Info`;
            document.getElementById('state-info-content').innerHTML = stateCosts.info.map(info => `<p>${info}</p>`).join('');
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>
</body>
</html>
